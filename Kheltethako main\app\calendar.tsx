import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Calendar as RNCalendar, DateData } from 'react-native-calendars';
import { 
  ArrowLeft, 
  Calendar as CalendarIcon, 
  Clock, 
  MapPin,
  Bell,
  Plus,
  Settings,
  Sync,
  Filter,
  List,
  Grid3X3
} from 'lucide-react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { getUserCalendarBookings, getUserNotificationPreferences } from '@/services/supabaseApi';
import { trackPageView, trackCustomEvent } from '@/services/analyticsService';

const { width } = Dimensions.get('window');

interface CalendarBooking {
  booking_id: number;
  booking_number: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  duration_hours: number;
  venue_name?: string;
  coach_name?: string;
  sport_name?: string;
  status: string;
  total_amount: number;
  venue_address?: string;
  coach_phone?: string;
  has_reminder: boolean;
  calendar_synced: boolean;
}

interface NotificationPreferences {
  booking_reminders_enabled: boolean;
  reminder_24h_enabled: boolean;
  reminder_2h_enabled: boolean;
  reminder_30m_enabled: boolean;
  calendar_sync_enabled: boolean;
}

export default function CalendarScreen() {
  const { user } = useAuth();
  const { colors, isDark } = useTheme();
  const { t } = useLanguage();
  
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [bookings, setBookings] = useState<CalendarBooking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [viewMode, setViewMode] = useState<'calendar' | 'list'>('calendar');
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);

  useEffect(() => {
    if (user) {
      trackPageView('/calendar', user.id);
      fetchCalendarData();
      fetchNotificationPreferences();
    }
  }, [user]);

  const fetchCalendarData = async () => {
    try {
      setIsLoading(true);
      if (user?.id) {
        const startDate = new Date();
        startDate.setDate(1); // Start of current month
        const endDate = new Date();
        endDate.setMonth(endDate.getMonth() + 2); // Next 2 months
        
        const data = await getUserCalendarBookings(
          user.id,
          startDate.toISOString().split('T')[0],
          endDate.toISOString().split('T')[0]
        );
        setBookings(data);
      }
    } catch (error) {
      console.error('Error fetching calendar data:', error);
      Alert.alert('Error', 'Failed to load calendar data. Please try again.');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const fetchNotificationPreferences = async () => {
    try {
      if (user?.id) {
        const prefs = await getUserNotificationPreferences(user.id);
        setPreferences(prefs);
      }
    } catch (error) {
      console.error('Error fetching notification preferences:', error);
    }
  };

  const onRefresh = () => {
    setIsRefreshing(true);
    fetchCalendarData();
  };

  const handleDateSelect = (day: DateData) => {
    setSelectedDate(day.dateString);
  };

  const getMarkedDates = () => {
    const marked: any = {};
    
    bookings.forEach(booking => {
      const date = booking.booking_date;
      if (!marked[date]) {
        marked[date] = { dots: [] };
      }
      
      // Add dot based on booking status
      const dotColor = getStatusColor(booking.status);
      marked[date].dots.push({
        color: dotColor,
        selectedDotColor: dotColor,
      });
    });

    // Mark selected date
    if (marked[selectedDate]) {
      marked[selectedDate].selected = true;
      marked[selectedDate].selectedColor = colors.primary;
    } else {
      marked[selectedDate] = {
        selected: true,
        selectedColor: colors.primary,
      };
    }

    return marked;
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return '#10B981';
      case 'pending':
        return '#F59E0B';
      case 'completed':
        return '#3B82F6';
      case 'cancelled':
        return '#EF4444';
      default:
        return '#6B7280';
    }
  };

  const getSelectedDateBookings = () => {
    return bookings.filter(booking => booking.booking_date === selectedDate);
  };

  const handleBookingPress = (booking: CalendarBooking) => {
    router.push(`/booking-details/${booking.booking_id}`);
  };

  const handleAddBooking = () => {
    router.push({
      pathname: '/explore',
      params: { selectedDate }
    });
  };

  const handleSyncCalendar = async () => {
    if (user) {
      await trackCustomEvent('calendar_sync_requested', user.id, {
        bookings_count: bookings.length,
      });
    }
    
    Alert.alert(
      'Calendar Sync',
      'This feature will sync your bookings with your device calendar. Would you like to enable it?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Enable', onPress: () => router.push('/notification-settings') },
      ]
    );
  };

  const renderBookingCard = (booking: CalendarBooking) => (
    <TouchableOpacity
      key={booking.booking_id}
      style={[
        styles.bookingCard,
        { 
          backgroundColor: colors.card,
          borderLeftColor: getStatusColor(booking.status),
        }
      ]}
      onPress={() => handleBookingPress(booking)}
    >
      <View style={styles.bookingHeader}>
        <View style={styles.bookingTime}>
          <Clock size={16} color={colors.textTertiary} />
          <Text style={[styles.timeText, { color: colors.text }]}>
            {booking.start_time} - {booking.end_time}
          </Text>
        </View>
        <View style={styles.bookingStatus}>
          <View style={[
            styles.statusDot,
            { backgroundColor: getStatusColor(booking.status) }
          ]} />
          <Text style={[
            styles.statusText,
            { color: getStatusColor(booking.status) }
          ]}>
            {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
          </Text>
        </View>
      </View>

      <Text style={[styles.bookingTitle, { color: colors.text }]}>
        {booking.venue_name || booking.coach_name}
      </Text>
      
      {booking.sport_name && (
        <Text style={[styles.bookingSport, { color: colors.textTertiary }]}>
          {booking.sport_name}
        </Text>
      )}

      {(booking.venue_address || booking.coach_phone) && (
        <View style={styles.bookingLocation}>
          <MapPin size={14} color={colors.textTertiary} />
          <Text style={[styles.locationText, { color: colors.textTertiary }]}>
            {booking.venue_address || `Coach: ${booking.coach_phone}`}
          </Text>
        </View>
      )}

      <View style={styles.bookingFooter}>
        <Text style={[styles.bookingPrice, { color: colors.primary }]}>
          ৳{booking.total_amount}
        </Text>
        <View style={styles.bookingIcons}>
          {booking.has_reminder && (
            <Bell size={16} color={colors.success} />
          )}
          {booking.calendar_synced && (
            <Sync size={16} color={colors.info} />
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderCalendarView = () => (
    <>
      <RNCalendar
        current={selectedDate}
        onDayPress={handleDateSelect}
        markingType="multi-dot"
        markedDates={getMarkedDates()}
        theme={{
          backgroundColor: colors.background,
          calendarBackground: colors.background,
          textSectionTitleColor: colors.textSecondary,
          selectedDayBackgroundColor: colors.primary,
          selectedDayTextColor: '#FFFFFF',
          todayTextColor: colors.primary,
          dayTextColor: colors.text,
          textDisabledColor: colors.disabled,
          dotColor: colors.primary,
          selectedDotColor: '#FFFFFF',
          arrowColor: colors.primary,
          monthTextColor: colors.text,
          indicatorColor: colors.primary,
          textDayFontFamily: 'Inter-Regular',
          textMonthFontFamily: 'Inter-SemiBold',
          textDayHeaderFontFamily: 'Inter-Medium',
          textDayFontSize: 16,
          textMonthFontSize: 18,
          textDayHeaderFontSize: 14,
        }

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: 'Inter-SemiBold',
    flex: 1,
    textAlign: 'center',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerButton: {
    padding: 8,
  },
  calendar: {
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  selectedDateSection: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  selectedDateTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 16,
  },
  bookingsList: {
    flex: 1,
  },
  listContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  bookingCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  bookingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  bookingTime: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  timeText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  bookingStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    textTransform: 'capitalize',
  },
  bookingTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  bookingSport: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginBottom: 8,
  },
  bookingLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 12,
  },
  locationText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    flex: 1,
  },
  bookingFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  bookingPrice: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
  },
  bookingIcons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 48,
    paddingHorizontal: 32,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  addBookingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  addBookingText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});}
        style={[styles.calendar, { backgroundColor: colors.background }]}
      />

      {/* Selected Date Bookings */}
      <View style={styles.selectedDateSection}>
        <Text style={[styles.selectedDateTitle, { color: colors.text }]}>
          {new Date(selectedDate).toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          })}
        </Text>
        
        <ScrollView 
          style={styles.bookingsList}
          showsVerticalScrollIndicator={false}
        >
          {getSelectedDateBookings().length > 0 ? (
            getSelectedDateBookings().map(renderBookingCard)
          ) : (
            <View style={styles.emptyState}>
              <CalendarIcon size={48} color={colors.disabled} />
              <Text style={[styles.emptyStateTitle, { color: colors.textSecondary }]}>
                No bookings for this date
              </Text>
              <TouchableOpacity
                style={[styles.addBookingButton, { backgroundColor: colors.primary }]}
                onPress={handleAddBooking}
              >
                <Plus size={16} color="#FFFFFF" />
                <Text style={styles.addBookingText}>Add Booking</Text>
              </TouchableOpacity>
            </View>
          )}
        </ScrollView>
      </View>
    </>
  );

  const renderListView = () => (
    <ScrollView 
      style={styles.listContainer}
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={onRefresh}
          colors={[colors.primary]}
          tintColor={colors.primary}
        />
      }
    >
      {bookings.length > 0 ? (
        bookings.map(renderBookingCard)
      ) : (
        <View style={styles.emptyState}>
          <CalendarIcon size={64} color={colors.disabled} />
          <Text style={[styles.emptyStateTitle, { color: colors.text }]}>
            No upcoming bookings
          </Text>
          <Text style={[styles.emptyStateText, { color: colors.textTertiary }]}>
            Your future bookings will appear here
          </Text>
          <TouchableOpacity
            style={[styles.addBookingButton, { backgroundColor: colors.primary }]}
            onPress={() => router.push('/explore')}
          >
            <Plus size={16} color="#FFFFFF" />
            <Text style={styles.addBookingText}>Book Now</Text>
          </TouchableOpacity>
        </View>
      )}
    </ScrollView>
  );

  return (
    <SafeAreaView 
      style={[styles.container, { backgroundColor: colors.background }]} 
      edges={['top', 'left', 'right']}
    >
      {/* Header */}
      <View style={[
        styles.header,
        { 
          backgroundColor: colors.background,
          borderBottomColor: colors.border,
        }
      ]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        
        <Text style={[styles.title, { color: colors.text }]}>
          {t('nav.calendar') || 'Calendar'}
        </Text>

        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setViewMode(viewMode === 'calendar' ? 'list' : 'calendar')}
          >
            {viewMode === 'calendar' ? (
              <List size={20} color={colors.text} />
            ) : (
              <Grid3X3 size={20} color={colors.text} />
            )}
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleSyncCalendar}
          >
            <Sync size={20} color={colors.text} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => router.push('/notification-settings')}
          >
            <Settings size={20} color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Content */}
      {viewMode === 'calendar' ? renderCalendarView() : renderListView()}
    </SafeAreaView>
  );
}
