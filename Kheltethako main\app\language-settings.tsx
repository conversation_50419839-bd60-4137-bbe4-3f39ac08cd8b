import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { 
  ArrowLeft, 
  Globe,
  Check,
  Info,
  Download,
  Smartphone
} from 'lucide-react-native';
import { useLanguage, SupportedLanguage, LANGUAGE_OPTIONS } from '@/contexts/LanguageContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { trackPageView, trackCustomEvent } from '@/services/analyticsService';

export default function LanguageSettingsScreen() {
  const { user } = useAuth();
  const { colors } = useTheme();
  const { language, setLanguage, t, isRTL } = useLanguage();

  React.useEffect(() => {
    if (user) {
      trackPageView('/language-settings', user.id);
    }
  }, [user]);

  const handleLanguageChange = async (newLanguage: SupportedLanguage) => {
    if (newLanguage === language) return;

    Alert.alert(
      t('settings.language_change_title') || 'Change Language',
      t('settings.language_change_message') || 'The app will restart to apply the new language. Continue?',
      [
        {
          text: t('common.cancel') || 'Cancel',
          style: 'cancel',
        },
        {
          text: t('common.confirm') || 'Confirm',
          onPress: async () => {
            setLanguage(newLanguage);
            
            // Track analytics
            if (user) {
              await trackCustomEvent('language_changed', user.id, {
                from_language: language,
                to_language: newLanguage,
              });
            }

            // Show success message
            Alert.alert(
              t('common.success') || 'Success',
              t('settings.language_changed') || 'Language changed successfully',
              [
                {
                  text: t('common.done') || 'Done',
                  onPress: () => router.back(),
                },
              ]
            );
          },
        },
      ]
    );
  };

  const renderLanguageOption = (option: typeof LANGUAGE_OPTIONS[0]) => (
    <TouchableOpacity
      key={option.code}
      style={[
        styles.languageOption,
        { 
          backgroundColor: colors.card,
          borderColor: colors.border,
        },
        language === option.code && {
          borderColor: colors.primary,
          backgroundColor: colors.primaryLight,
        }
      ]}
      onPress={() => handleLanguageChange(option.code)}
    >
      <View style={styles.languageOptionContent}>
        <View style={styles.languageFlag}>
          <Text style={styles.flagEmoji}>{option.flag}</Text>
        </View>
        <View style={styles.languageInfo}>
          <Text style={[
            styles.languageName,
            { color: colors.text }
          ]}>
            {option.name}
          </Text>
          <Text style={[
            styles.languageNativeName,
            { color: colors.textTertiary }
          ]}>
            {option.nativeName}
          </Text>
        </View>
        {language === option.code && (
          <View style={[
            styles.languageCheck,
            { backgroundColor: colors.primary }
          ]}>
            <Check size={16} color="#FFFFFF" />
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView 
      style={[styles.container, { backgroundColor: colors.background }]} 
      edges={['top', 'left', 'right']}
    >
      {/* Header */}
      <View style={[
        styles.header,
        { 
          backgroundColor: colors.background,
          borderBottomColor: colors.border,
        }
      ]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: colors.text }]}>
          {t('settings.language') || 'Language Settings'}
        </Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView 
        style={styles.content}
        showsVerticalScrollIndicator={false}
      >
        {/* Current Language */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {t('settings.current_language') || 'Current Language'}
          </Text>
          <View style={[
            styles.currentLanguageCard,
            { 
              backgroundColor: colors.card,
              borderColor: colors.border,
            }
          ]}>
            <View style={styles.currentLanguageHeader}>
              <Globe size={20} color={colors.primary} />
              <Text style={[styles.currentLanguageTitle, { color: colors.text }]}>
                {LANGUAGE_OPTIONS.find(opt => opt.code === language)?.name || 'English'}
              </Text>
              <Text style={styles.currentLanguageFlag}>
                {LANGUAGE_OPTIONS.find(opt => opt.code === language)?.flag || '🇺🇸'}
              </Text>
            </View>
            <Text style={[styles.currentLanguageDescription, { color: colors.textSecondary }]}>
              {t('settings.current_language_desc') || 'This is your currently selected language for the app interface.'}
            </Text>
          </View>
        </View>

        {/* Available Languages */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {t('settings.available_languages') || 'Available Languages'}
          </Text>
          <Text style={[styles.sectionDescription, { color: colors.textTertiary }]}>
            {t('settings.language_selection_desc') || 'Select your preferred language for the app interface'}
          </Text>
          <View style={styles.languageOptions}>
            {LANGUAGE_OPTIONS.map(renderLanguageOption)}
          </View>
        </View>

        {/* Language Features */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {t('settings.language_features') || 'Language Features'}
          </Text>
          <View style={[
            styles.featuresCard,
            { 
              backgroundColor: colors.card,
              borderColor: colors.border,
            }
          ]}>
            <View style={styles.featureItem}>
              <View style={styles.featureIcon}>
                <Smartphone size={20} color={colors.text} />
              </View>
              <View style={styles.featureText}>
                <Text style={[styles.featureTitle, { color: colors.text }]}>
                  {t('settings.rtl_support') || 'RTL Support'}
                </Text>
                <Text style={[styles.featureDescription, { color: colors.textTertiary }]}>
                  {t('settings.rtl_support_desc') || 'Right-to-left text direction for Arabic and Urdu'}
                </Text>
              </View>
            </View>

            <View style={[styles.separator, { backgroundColor: colors.border }]} />

            <View style={styles.featureItem}>
              <View style={styles.featureIcon}>
                <Download size={20} color={colors.text} />
              </View>
              <View style={styles.featureText}>
                <Text style={[styles.featureTitle, { color: colors.text }]}>
                  {t('settings.offline_support') || 'Offline Support'}
                </Text>
                <Text style={[styles.featureDescription, { color: colors.textTertiary }]}>
                  {t('settings.offline_support_desc') || 'Language packs are downloaded for offline use'}
                </Text>
              </View>
            </View>

            <View style={[styles.separator, { backgroundColor: colors.border }]} />

            <View style={styles.featureItem}>
              <View style={styles.featureIcon}>
                <Globe size={20} color={colors.text} />
              </View>
              <View style={styles.featureText}>
                <Text style={[styles.featureTitle, { color: colors.text }]}>
                  {t('settings.regional_formats') || 'Regional Formats'}
                </Text>
                <Text style={[styles.featureDescription, { color: colors.textTertiary }]}>
                  {t('settings.regional_formats_desc') || 'Date, time, and number formats based on your language'}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Language Info */}
        <View style={styles.section}>
          <View style={[
            styles.infoCard,
            { 
              backgroundColor: colors.surface,
              borderColor: colors.borderLight,
            }
          ]}>
            <View style={styles.infoHeader}>
              <Info size={20} color={colors.info} />
              <Text style={[styles.infoTitle, { color: colors.text }]}>
                {t('settings.language_info') || 'About Languages'}
              </Text>
            </View>
            <Text style={[styles.infoText, { color: colors.textSecondary }]}>
              {t('settings.language_info_desc') || 
                '• All languages are fully translated by native speakers\n' +
                '• Language changes take effect immediately\n' +
                '• Your language preference is saved and synced\n' +
                '• More languages will be added based on user demand'
              }
            </Text>
          </View>
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: 'Inter-SemiBold',
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginBottom: 16,
    lineHeight: 20,
  },
  currentLanguageCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
  },
  currentLanguageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  currentLanguageTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    flex: 1,
    marginLeft: 8,
  },
  currentLanguageFlag: {
    fontSize: 24,
  },
  currentLanguageDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
  languageOptions: {
    gap: 12,
  },
  languageOption: {
    borderRadius: 12,
    borderWidth: 2,
    overflow: 'hidden',
  },
  languageOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  languageFlag: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  flagEmoji: {
    fontSize: 24,
  },
  languageInfo: {
    flex: 1,
  },
  languageName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  languageNativeName: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  languageCheck: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  featuresCard: {
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  featureIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  featureText: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  featureDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  separator: {
    height: 1,
    marginHorizontal: 16,
  },
  infoCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginLeft: 8,
  },
  infoText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
  bottomSpacing: {
    height: 32,
  },
});
