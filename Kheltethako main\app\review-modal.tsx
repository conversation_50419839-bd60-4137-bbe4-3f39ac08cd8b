import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Modal,
  ActivityIndicator,
  Alert,
  Image,
} from 'react-native';
import { Star, X, Camera, CheckCircle } from 'lucide-react-native';
import { useAuth } from '@/contexts/AuthContext';
import { createReview } from '@/services/supabaseApi';
import { trackReviewSubmitted } from '@/services/analyticsService';

type ReviewModalProps = {
  visible: boolean;
  onClose: () => void;
  onSubmit: (rating: number, review: string) => void;
  type: 'venue' | 'coach';
  venueId?: number;
  coachId?: number;
  bookingId?: number;
  venueName?: string;
  coachName?: string;
  venueImage?: string;
  coachImage?: string;
};

export default function ReviewModal({
  visible,
  onClose,
  onSubmit,
  type,
  venueId,
  coachId,
  bookingId,
  venueName,
  coachName,
  venueImage,
  coachImage,
}: ReviewModalProps) {
  const { user } = useAuth();
  const [rating, setRating] = useState(0);
  const [review, setReview] = useState('');
  const [title, setTitle] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async () => {
    if (!user) {
      Alert.alert('Authentication Required', 'Please sign in to submit a review');
      return;
    }

    if (!rating || !review.trim()) {
      Alert.alert('Incomplete Review', 'Please provide a rating and review');
      return;
    }

    try {
      setIsSubmitting(true);

      const reviewData = {
        user_id: user.id,
        venue_id: venueId,
        coach_id: coachId,
        booking_id: bookingId,
        rating,
        title: title.trim() || undefined,
        comment: review.trim(),
      };

      const createdReview = await createReview(reviewData);

      if (createdReview) {
        // Track analytics
        await trackReviewSubmitted(
          user.id,
          rating,
          venueId?.toString(),
          coachId?.toString(),
          bookingId?.toString()
        );

        setIsSubmitted(true);

        // Call the original onSubmit callback
        onSubmit(rating, review);

        // Show success and close after delay
        setTimeout(() => {
          setIsSubmitted(false);
          setRating(0);
          setReview('');
          setTitle('');
          onClose();
        }, 2000);
      } else {
        throw new Error('Failed to create review');
      }
    } catch (error) {
      console.error('Error submitting review:', error);
      Alert.alert('Error', 'Failed to submit review. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              Rate this {type === 'venue' ? 'Venue' : 'Coach'}
            </Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
            >
              <X size={24} color="#6B7280" />
            </TouchableOpacity>
          </View>

          {/* Venue/Coach Info */}
          {(venueName || coachName) && (
            <View style={styles.infoContainer}>
              <Image
                source={{
                  uri: type === 'venue' ? venueImage : coachImage ||
                    'https://images.unsplash.com/photo-1459865264687-595d652de67e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
                }}
                style={styles.infoImage}
              />
              <View style={styles.infoDetails}>
                <Text style={styles.infoName}>
                  {type === 'venue' ? venueName : coachName}
                </Text>
                <Text style={styles.infoType}>
                  {type === 'venue' ? 'Sports Venue' : 'Professional Coach'}
                </Text>
              </View>
            </View>
          )}

          {isSubmitted ? (
            <View style={styles.successContainer}>
              <CheckCircle size={64} color="#10B981" />
              <Text style={styles.successTitle}>Review Submitted!</Text>
              <Text style={styles.successText}>
                Thank you for your feedback. Your review helps others make better decisions.
              </Text>
            </View>
          ) : (
            <>
              <View style={styles.starsContainer}>
                {[1, 2, 3, 4, 5].map((star) => (
                  <TouchableOpacity
                    key={star}
                    onPress={() => setRating(star)}
                    style={styles.starButton}
                  >
                    <Star
                      size={32}
                      color="#F59E0B"
                      fill={star <= rating ? '#F59E0B' : 'transparent'}
                    />
                  </TouchableOpacity>
                ))}
              </View>
          
          <Text style={styles.ratingText}>
            {rating === 0
              ? 'Tap a star to rate'
              : rating === 5
              ? 'Excellent!'
              : rating === 4
              ? 'Very Good'
              : rating === 3
              ? 'Good'
              : rating === 2
              ? 'Fair'
              : 'Poor'}
          </Text>

              <Text style={styles.label}>Review Title (Optional)</Text>
              <TextInput
                style={styles.titleInput}
                placeholder="Summarize your experience..."
                value={title}
                onChangeText={setTitle}
                maxLength={100}
              />

              <Text style={styles.label}>Your Review</Text>
              <TextInput
                style={styles.reviewInput}
                placeholder="Share your experience in detail..."
                multiline
                numberOfLines={4}
                value={review}
                onChangeText={setReview}
                maxLength={500}
              />
              <Text style={styles.characterCount}>
                {review.length}/500 characters
              </Text>

              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={onClose}
                  disabled={isSubmitting}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.submitButton,
                    (!rating || !review.trim() || isSubmitting) && styles.submitButtonDisabled,
                  ]}
                  onPress={handleSubmit}
                  disabled={!rating || !review.trim() || isSubmitting}
                >
                  {isSubmitting ? (
                    <ActivityIndicator color="#FFFFFF" size="small" />
                  ) : (
                    <Text style={styles.submitButtonText}>Submit Review</Text>
                  )}
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 20,
  },
  starsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 12,
  },
  starButton: {
    padding: 4,
  },
  ratingText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 20,
  },
  label: {
    fontSize: 13,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: 6,
  },
  reviewInput: {
    height: 100,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 6,
    padding: 12,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    backgroundColor: '#F9FAFB',
    textAlignVertical: 'top',
    marginBottom: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    height: 44,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  cancelButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  submitButton: {
    flex: 1,
    height: 44,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#EF4444',
  },
  submitButtonDisabled: {
    backgroundColor: '#F3F4F6',
  },
  submitButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  closeButton: {
    padding: 4,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  infoImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  infoDetails: {
    flex: 1,
  },
  infoName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 2,
  },
  infoType: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  successContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  successTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8,
  },
  successText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
  titleInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    marginBottom: 16,
  },
  characterCount: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    textAlign: 'right',
    marginTop: 4,
    marginBottom: 20,
  },
});