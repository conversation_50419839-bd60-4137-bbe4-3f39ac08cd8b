import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface AnalyticsEvent {
  event_type: string;
  user_id?: string;
  session_id?: string;
  page_url?: string;
  venue_id?: string;
  coach_id?: string;
  search_query?: string;
  metadata?: Record<string, any>;
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    );

    // Parse request body
    const { event_type, user_id, session_id, page_url, venue_id, coach_id, search_query, metadata }: AnalyticsEvent = await req.json();

    console.log('📊 Analytics event received:', { event_type, user_id, session_id });

    // Validate required fields
    if (!event_type) {
      return new Response(
        JSON.stringify({ error: 'event_type is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Insert analytics event into database
    const { data, error } = await supabaseClient
      .from('analytics_events')
      .insert({
        event_type,
        user_id: user_id || null,
        session_id: session_id || null,
        page_url: page_url || null,
        venue_id: venue_id || null,
        coach_id: coach_id || null,
        search_query: search_query || null,
        metadata: metadata || {},
        created_at: new Date().toISOString(),
      });

    if (error) {
      console.error('❌ Error inserting analytics event:', error);
      
      // If table doesn't exist, create it
      if (error.code === '42P01') {
        console.log('📝 Creating analytics_events table...');
        
        const { error: createError } = await supabaseClient.rpc('create_analytics_table');
        
        if (createError) {
          console.error('❌ Error creating analytics table:', createError);
          return new Response(
            JSON.stringify({ error: 'Failed to create analytics table', details: createError }),
            { 
              status: 500, 
              headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
            }
          );
        }

        // Retry insertion
        const { data: retryData, error: retryError } = await supabaseClient
          .from('analytics_events')
          .insert({
            event_type,
            user_id: user_id || null,
            session_id: session_id || null,
            page_url: page_url || null,
            venue_id: venue_id || null,
            coach_id: coach_id || null,
            search_query: search_query || null,
            metadata: metadata || {},
            created_at: new Date().toISOString(),
          });

        if (retryError) {
          console.error('❌ Error on retry:', retryError);
          return new Response(
            JSON.stringify({ error: 'Failed to insert analytics event', details: retryError }),
            { 
              status: 500, 
              headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
            }
          );
        }

        console.log('✅ Analytics event inserted on retry');
        return new Response(
          JSON.stringify({ success: true, message: 'Analytics event tracked successfully' }),
          { 
            status: 200, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        );
      }

      return new Response(
        JSON.stringify({ error: 'Failed to insert analytics event', details: error }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    console.log('✅ Analytics event tracked successfully');

    return new Response(
      JSON.stringify({ success: true, message: 'Analytics event tracked successfully' }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('💥 Analytics function error:', error);
    
    return new Response(
      JSON.stringify({ error: 'Internal server error', details: error.message }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});
