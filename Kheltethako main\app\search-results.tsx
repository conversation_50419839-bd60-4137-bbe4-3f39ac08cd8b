import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Image,
  Dimensions,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { 
  ArrowLeft, 
  Search, 
  Filter, 
  MapPin, 
  Star, 
  Clock,
  Grid,
  List,
  SlidersHorizontal,
  X
} from 'lucide-react-native';
import { searchVenues, getRecommendedCoaches } from '@/services/supabaseApi';
import { trackSearch, trackPageView } from '@/services/analyticsService';
import { useAuth } from '@/contexts/AuthContext';

const { width } = Dimensions.get('window');

interface SearchResult {
  id: number;
  type: 'venue' | 'coach';
  name: string;
  image_url: string;
  location: string;
  rating: number;
  reviews_count: number;
  price_per_hour?: number;
  specialty?: string;
  sport_name?: string;
  sport_icon?: string;
  distance?: number;
}

export default function SearchResultsScreen() {
  const { user } = useAuth();
  const params = useLocalSearchParams();
  const [searchQuery, setSearchQuery] = useState(params.query as string || '');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [activeTab, setActiveTab] = useState<'all' | 'venues' | 'coaches'>('all');
  const [sortBy, setSortBy] = useState<'relevance' | 'rating' | 'price' | 'distance'>('relevance');

  useEffect(() => {
    trackPageView('/search-results', user?.id);
    if (searchQuery) {
      performSearch();
    }
  }, [searchQuery, activeTab, sortBy]);

  const performSearch = async () => {
    try {
      setIsLoading(true);
      
      let allResults: SearchResult[] = [];

      // Search venues
      if (activeTab === 'all' || activeTab === 'venues') {
        const venues = await searchVenues({
          query: searchQuery,
          location: params.location as string,
          sport_id: params.sportId as string,
          min_price: params.minPrice ? parseInt(params.minPrice as string) : undefined,
          max_price: params.maxPrice ? parseInt(params.maxPrice as string) : undefined,
          min_rating: params.minRating ? parseFloat(params.minRating as string) : undefined,
        });

        const venueResults: SearchResult[] = venues.map(venue => ({
          id: venue.id,
          type: 'venue',
          name: venue.name,
          image_url: venue.image_url,
          location: venue.location,
          rating: venue.rating,
          reviews_count: venue.reviews_count,
          price_per_hour: venue.price_per_hour,
          sport_name: venue.sport_name,
          sport_icon: venue.sport_icon,
        }));

        allResults = [...allResults, ...venueResults];
      }

      // Search coaches
      if (activeTab === 'all' || activeTab === 'coaches') {
        const coaches = await getRecommendedCoaches(params.sportId as string);
        
        const coachResults: SearchResult[] = coaches
          .filter(coach => 
            !searchQuery || 
            coach.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            coach.specialty?.toLowerCase().includes(searchQuery.toLowerCase()) ||
            coach.location?.toLowerCase().includes(searchQuery.toLowerCase())
          )
          .map(coach => ({
            id: coach.id,
            type: 'coach',
            name: coach.name,
            image_url: coach.image_url,
            location: coach.location,
            rating: coach.rating,
            reviews_count: coach.reviews_count,
            price_per_hour: coach.price_per_session,
            specialty: coach.specialty,
            sport_name: coach.sport_name,
            sport_icon: coach.sport_icon,
          }));

        allResults = [...allResults, ...coachResults];
      }

      // Sort results
      allResults = sortResults(allResults);
      
      setResults(allResults);
      
      // Track search analytics
      await trackSearch(searchQuery, user?.id, allResults.length);
      
    } catch (error) {
      console.error('Error performing search:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const sortResults = (results: SearchResult[]) => {
    switch (sortBy) {
      case 'rating':
        return results.sort((a, b) => b.rating - a.rating);
      case 'price':
        return results.sort((a, b) => (a.price_per_hour || 0) - (b.price_per_hour || 0));
      case 'distance':
        return results.sort((a, b) => (a.distance || 0) - (b.distance || 0));
      default:
        return results;
    }
  };

  const handleResultPress = (result: SearchResult) => {
    if (result.type === 'venue') {
      router.push(`/venues/${result.id}`);
    } else {
      router.push(`/coaches/${result.id}`);
    }
  };

  const clearSearch = () => {
    setSearchQuery('');
    setResults([]);
  };

  const openFilters = () => {
    router.push({
      pathname: '/filters',
      params: {
        query: searchQuery,
        location: params.location,
        sportId: params.sportId,
      }
    });
  };

  const renderGridItem = (result: SearchResult) => (
    <TouchableOpacity
      key={`${result.type}-${result.id}`}
      style={styles.gridItem}
      onPress={() => handleResultPress(result)}
    >
      <Image source={{ uri: result.image_url }} style={styles.gridImage} />
      <View style={styles.gridContent}>
        <View style={styles.typeIndicator}>
          <Text style={styles.typeText}>{result.type}</Text>
        </View>
        <Text style={styles.gridTitle} numberOfLines={2}>{result.name}</Text>
        <View style={styles.gridLocation}>
          <MapPin size={12} color="#6B7280" />
          <Text style={styles.gridLocationText} numberOfLines={1}>{result.location}</Text>
        </View>
        <View style={styles.gridFooter}>
          <View style={styles.gridRating}>
            <Star size={12} color="#F59E0B" fill="#F59E0B" />
            <Text style={styles.gridRatingText}>{result.rating.toFixed(1)}</Text>
          </View>
          {result.price_per_hour && (
            <Text style={styles.gridPrice}>৳{result.price_per_hour}/hr</Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderListItem = (result: SearchResult) => (
    <TouchableOpacity
      key={`${result.type}-${result.id}`}
      style={styles.listItem}
      onPress={() => handleResultPress(result)}
    >
      <Image source={{ uri: result.image_url }} style={styles.listImage} />
      <View style={styles.listContent}>
        <View style={styles.listHeader}>
          <Text style={styles.listTitle} numberOfLines={1}>{result.name}</Text>
          <View style={styles.typeIndicator}>
            <Text style={styles.typeText}>{result.type}</Text>
          </View>
        </View>
        <Text style={styles.listSpecialty} numberOfLines={1}>
          {result.specialty || result.sport_name}
        </Text>
        <View style={styles.listLocation}>
          <MapPin size={14} color="#6B7280" />
          <Text style={styles.listLocationText} numberOfLines={1}>{result.location}</Text>
        </View>
        <View style={styles.listFooter}>
          <View style={styles.listRating}>
            <Star size={14} color="#F59E0B" fill="#F59E0B" />
            <Text style={styles.listRatingText}>{result.rating.toFixed(1)}</Text>
            <Text style={styles.listReviewCount}>({result.reviews_count})</Text>
          </View>
          {result.price_per_hour && (
            <Text style={styles.listPrice}>৳{result.price_per_hour}/hr</Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const filteredResults = results.filter(result => {
    if (activeTab === 'all') return true;
    return result.type === activeTab.slice(0, -1); // Remove 's' from 'venues'/'coaches'
  });

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color="#111827" />
        </TouchableOpacity>
        
        <View style={styles.searchContainer}>
          <Search size={20} color="#6B7280" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search venues, coaches, sports..."
            placeholderTextColor="#9CA3AF"
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={performSearch}
            returnKeyType="search"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={clearSearch}>
              <X size={20} color="#6B7280" />
            </TouchableOpacity>
          )}
        </View>

        <TouchableOpacity
          style={styles.filterButton}
          onPress={openFilters}
        >
          <SlidersHorizontal size={20} color="#EF4444" />
        </TouchableOpacity>
      </View>

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        {[
          { key: 'all', label: 'All' },
          { key: 'venues', label: 'Venues' },
          { key: 'coaches', label: 'Coaches' },
        ].map((tab) => (
          <TouchableOpacity
            key={tab.key}
            style={[
              styles.tab,
              activeTab === tab.key && styles.activeTab
            ]}
            onPress={() => setActiveTab(tab.key as any)}
          >
            <Text style={[
              styles.tabText,
              activeTab === tab.key && styles.activeTabText
            ]}>
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Controls */}
      <View style={styles.controlsContainer}>
        <View style={styles.sortContainer}>
          <Text style={styles.sortLabel}>Sort by:</Text>
          <TouchableOpacity
            style={styles.sortButton}
            onPress={() => {
              const options = ['relevance', 'rating', 'price', 'distance'];
              const currentIndex = options.indexOf(sortBy);
              const nextIndex = (currentIndex + 1) % options.length;
              setSortBy(options[nextIndex] as any);
            }}
          >
            <Text style={styles.sortButtonText}>
              {sortBy.charAt(0).toUpperCase() + sortBy.slice(1)}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.viewModeContainer}>
          <TouchableOpacity
            style={[styles.viewModeButton, viewMode === 'grid' && styles.activeViewMode]}
            onPress={() => setViewMode('grid')}
          >
            <Grid size={16} color={viewMode === 'grid' ? '#EF4444' : '#6B7280'} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.viewModeButton, viewMode === 'list' && styles.activeViewMode]}
            onPress={() => setViewMode('list')}
          >
            <List size={16} color={viewMode === 'list' ? '#EF4444' : '#6B7280'} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Results */}
      <View style={styles.resultsHeader}>
        <Text style={styles.resultsCount}>
          {isLoading ? 'Searching...' : `${filteredResults.length} results found`}
        </Text>
      </View>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#EF4444" />
          <Text style={styles.loadingText}>Searching...</Text>
        </View>
      ) : filteredResults.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Search size={64} color="#D1D5DB" />
          <Text style={styles.emptyTitle}>No results found</Text>
          <Text style={styles.emptyText}>
            Try adjusting your search terms or filters
          </Text>
        </View>
      ) : (
        <ScrollView 
          style={styles.resultsContainer}
          showsVerticalScrollIndicator={false}
        >
          {viewMode === 'grid' ? (
            <View style={styles.gridContainer}>
              {filteredResults.map(renderGridItem)}
            </View>
          ) : (
            <View style={styles.listContainer}>
              {filteredResults.map(renderListItem)}
            </View>
          )}
          <View style={styles.bottomSpacing} />
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    gap: 12,
  },
  backButton: {
    padding: 4,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: '#111827',
    fontFamily: 'Inter-Regular',
  },
  filterButton: {
    padding: 8,
    backgroundColor: '#FEF2F2',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FECACA',
  },
  tabsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  tab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#F9FAFB',
  },
  activeTab: {
    backgroundColor: '#EF4444',
  },
  tabText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  activeTabText: {
    color: '#FFFFFF',
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  sortContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sortLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginRight: 8,
  },
  sortButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#F9FAFB',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  sortButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
  },
  viewModeContainer: {
    flexDirection: 'row',
    backgroundColor: '#F9FAFB',
    borderRadius: 6,
    padding: 2,
  },
  viewModeButton: {
    padding: 6,
    borderRadius: 4,
  },
  activeViewMode: {
    backgroundColor: '#FFFFFF',
  },
  resultsHeader: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  resultsCount: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 12,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  resultsContainer: {
    flex: 1,
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 8,
    paddingTop: 8,
  },
  gridItem: {
    width: (width - 32) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginHorizontal: 8,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  gridImage: {
    width: '100%',
    height: 120,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  gridContent: {
    padding: 12,
  },
  typeIndicator: {
    alignSelf: 'flex-start',
    backgroundColor: '#EF4444',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginBottom: 8,
  },
  typeText: {
    fontSize: 10,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
    textTransform: 'uppercase',
  },
  gridTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
    lineHeight: 18,
  },
  gridLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  gridLocationText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 4,
    flex: 1,
  },
  gridFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  gridRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  gridRatingText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginLeft: 4,
  },
  gridPrice: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#EF4444',
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  listItem: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  listImage: {
    width: 80,
    height: 80,
  },
  listContent: {
    flex: 1,
    padding: 12,
  },
  listHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  listTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    flex: 1,
    marginRight: 8,
  },
  listSpecialty: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 4,
  },
  listLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  listLocationText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 4,
    flex: 1,
  },
  listFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  listRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  listRatingText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginLeft: 4,
  },
  listReviewCount: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    marginLeft: 4,
  },
  listPrice: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#EF4444',
  },
  bottomSpacing: {
    height: 20,
  },
});
