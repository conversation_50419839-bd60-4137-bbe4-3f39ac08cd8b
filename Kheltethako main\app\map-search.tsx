import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  Dimensions,
  Platform,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import MapView, { Marker, Region, PROVIDER_GOOGLE } from 'react-native-maps';
import * as Location from 'expo-location';
import { 
  ArrowLeft, 
  Search, 
  Filter, 
  MapPin, 
  Navigation,
  Layers,
  Plus,
  Minus,
  Target,
  List
} from 'lucide-react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { searchVenuesByLocation, searchCoachesByLocation } from '@/services/supabaseApi';
import { trackPageView, trackCustomEvent } from '@/services/analyticsService';

const { width, height } = Dimensions.get('window');

interface MapSearchResult {
  id: number;
  name: string;
  latitude: number;
  longitude: number;
  distance_km: number;
  price_per_hour?: number;
  price_per_session?: number;
  rating: number;
  type: 'venue' | 'coach';
  sport_name?: string;
  sport_icon?: string;
  image_url?: string;
}

export default function MapSearchScreen() {
  const { user } = useAuth();
  const { colors } = useTheme();
  const { t } = useLanguage();
  const params = useLocalSearchParams();
  
  const [searchQuery, setSearchQuery] = useState(params.query as string || '');
  const [searchType, setSearchType] = useState<'venues' | 'coaches' | 'both'>('both');
  const [results, setResults] = useState<MapSearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  const [selectedResult, setSelectedResult] = useState<MapSearchResult | null>(null);
  const [mapType, setMapType] = useState<'standard' | 'satellite' | 'hybrid'>('standard');
  const [searchRadius, setSearchRadius] = useState(10); // km
  
  const mapRef = useRef<MapView>(null);

  const initialRegion: Region = {
    latitude: 23.7808875, // Dhaka coordinates
    longitude: 90.2792373,
    latitudeDelta: 0.1,
    longitudeDelta: 0.1,
  };

  useEffect(() => {
    if (user) {
      trackPageView('/map-search', user.id);
    }
    requestLocationPermission();
  }, [user]);

  useEffect(() => {
    if (userLocation) {
      performSearch();
    }
  }, [userLocation, searchQuery, searchType, searchRadius]);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Location Permission',
          'Please enable location access to find nearby venues and coaches.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Settings', onPress: () => Location.requestForegroundPermissionsAsync() },
          ]
        );
        return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });
      
      setUserLocation(location);
      
      // Center map on user location
      if (mapRef.current) {
        mapRef.current.animateToRegion({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          latitudeDelta: 0.05,
          longitudeDelta: 0.05,
        });
      }
    } catch (error) {
      console.error('Error getting location:', error);
      Alert.alert('Error', 'Failed to get your location. Using default location.');
    } finally {
      setIsLoading(false);
    }
  };

  const performSearch = async () => {
    if (!userLocation) return;

    try {
      setIsLoading(true);
      
      let allResults: MapSearchResult[] = [];

      // Search venues
      if (searchType === 'venues' || searchType === 'both') {
        const venues = await searchVenuesByLocation({
          userLat: userLocation.coords.latitude,
          userLon: userLocation.coords.longitude,
          searchRadius,
          searchQuery,
          sportFilter: params.sportId ? parseInt(params.sportId as string) : undefined,
        });

        const venueResults: MapSearchResult[] = venues.map(venue => ({
          id: venue.id,
          name: venue.name,
          latitude: venue.latitude,
          longitude: venue.longitude,
          distance_km: venue.distance_km,
          price_per_hour: venue.price_per_hour,
          rating: venue.rating,
          type: 'venue',
          sport_name: venue.sport_name,
          sport_icon: venue.sport_icon,
          image_url: venue.image_url,
        }));

        allResults = [...allResults, ...venueResults];
      }

      // Search coaches
      if (searchType === 'coaches' || searchType === 'both') {
        const coaches = await searchCoachesByLocation({
          userLat: userLocation.coords.latitude,
          userLon: userLocation.coords.longitude,
          searchRadius,
          searchQuery,
          sportFilter: params.sportId ? parseInt(params.sportId as string) : undefined,
        });

        const coachResults: MapSearchResult[] = coaches.map(coach => ({
          id: coach.id,
          name: coach.name,
          latitude: coach.latitude,
          longitude: coach.longitude,
          distance_km: coach.distance_km,
          price_per_session: coach.price_per_session,
          rating: coach.rating,
          type: 'coach',
          sport_name: coach.sport_name,
          sport_icon: coach.sport_icon,
          image_url: coach.image_url,
        }));

        allResults = [...allResults, ...coachResults];
      }

      setResults(allResults);

      // Track search analytics
      if (user) {
        await trackCustomEvent('map_search_performed', user.id, {
          query: searchQuery,
          type: searchType,
          radius: searchRadius,
          results_count: allResults.length,
        });
      }

    } catch (error) {
      console.error('Error performing map search:', error);
      Alert.alert('Error', 'Failed to search locations. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMarkerPress = (result: MapSearchResult) => {
    setSelectedResult(result);
    
    // Center map on selected marker
    if (mapRef.current) {
      mapRef.current.animateToRegion({
        latitude: result.latitude,
        longitude: result.longitude,
        latitudeDelta: 0.02,
        longitudeDelta: 0.02,
      });
    }
  };

  const handleResultPress = (result: MapSearchResult) => {
    if (result.type === 'venue') {
      router.push(`/venue/${result.id}`);
    } else {
      router.push(`/coach/${result.id}`);
    }
  };

  const centerOnUserLocation = () => {
    if (userLocation && mapRef.current) {
      mapRef.current.animateToRegion({
        latitude: userLocation.coords.latitude,
        longitude: userLocation.coords.longitude,
        latitudeDelta: 0.05,
        longitudeDelta: 0.05,
      });
    }
  };

  const adjustSearchRadius = (increase: boolean) => {
    const newRadius = increase 
      ? Math.min(searchRadius + 5, 50) 
      : Math.max(searchRadius - 5, 1);
    setSearchRadius(newRadius);
  };

  const getMarkerColor = (type: 'venue' | 'coach') => {
    return type === 'venue' ? '#EF4444' : '#3B82F6';
  };

  return (
    <SafeAreaView 
      style={[styles.container, { backgroundColor: colors.background }]} 
      edges={['top', 'left', 'right']}
    >
      {/* Header */}
      <View style={[
        styles.header,
        { 
          backgroundColor: colors.background,
          borderBottomColor: colors.border,
        }
      ]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        
        <View style={[styles.searchContainer, { backgroundColor: colors.surface }]}>
          <Search size={20} color={colors.textTertiary} />
          <TextInput
            style={[styles.searchInput, { color: colors.text }]}
            placeholder={t('common.search') || 'Search locations...'}
            placeholderTextColor={colors.placeholder}
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={performSearch}
            returnKeyType="search"
          />
        </View>

        <TouchableOpacity
          style={[styles.filterButton, { backgroundColor: colors.primaryLight }]}
          onPress={() => router.push('/filters')}
        >
          <Filter size={20} color={colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Search Type Selector */}
      <View style={[styles.typeSelector, { backgroundColor: colors.surface }]}>
        {[
          { key: 'both', label: 'All' },
          { key: 'venues', label: 'Venues' },
          { key: 'coaches', label: 'Coaches' },
        ].map((type) => (
          <TouchableOpacity
            key={type.key}
            style={[
              styles.typeButton,
              searchType === type.key && { backgroundColor: colors.primary }
            ]}
            onPress={() => setSearchType(type.key as any)}
          >
            <Text style={[
              styles.typeButtonText,
              { color: searchType === type.key ? '#FFFFFF' : colors.text }
            ]}>
              {type.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Map */}
      <View style={styles.mapContainer}>
        <MapView
          ref={mapRef}
          style={styles.map}
          provider={Platform.OS === 'android' ? PROVIDER_GOOGLE : undefined}
          initialRegion={initialRegion}
          mapType={mapType}
          showsUserLocation={true}
          showsMyLocationButton={false}
          showsCompass={true}
          showsScale={true}
        >
          {/* User Location Marker */}
          {userLocation && (
            <Marker
              coordinate={{
                latitude: userLocation.coords.latitude,
                longitude: userLocation.coords.longitude,
              }}
              title="Your Location"
              pinColor="#10B981"
            />
          )}

          {/* Search Results Markers */}
          {results.map((result) => (
            <Marker
              key={`${result.type}-${result.id}`}
              coordinate={{
                latitude: result.latitude,
                longitude: result.longitude,
              }}
              title={result.name}
              description={`${result.distance_km.toFixed(1)}km away • ⭐ ${result.rating}`}
              pinColor={getMarkerColor(result.type)}
              onPress={() => handleMarkerPress(result)}
            />
          ))}
        </MapView>

        {/* Map Controls */}
        <View style={styles.mapControls}>
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: colors.card }]}
            onPress={centerOnUserLocation}
          >
            <Target size={20} color={colors.text} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: colors.card }]}
            onPress={() => {
              const types = ['standard', 'satellite', 'hybrid'];
              const currentIndex = types.indexOf(mapType);
              const nextIndex = (currentIndex + 1) % types.length;
              setMapType(types[nextIndex] as any);
            }}
          >
            <Layers size={20} color={colors.text} />
          </TouchableOpacity>
        </View>

        {/* Search Radius Controls */}
        <View style={styles.radiusControls}>
          <TouchableOpacity
            style={[styles.radiusButton, { backgroundColor: colors.card }]}
            onPress={() => adjustSearchRadius(false)}
          >
            <Minus size={16} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.radiusText, { color: colors.text }]}>
            {searchRadius}km
          </Text>
          <TouchableOpacity
            style={[styles.radiusButton, { backgroundColor: colors.card }]}
            onPress={() => adjustSearchRadius(true)}
          >
            <Plus size={16} color={colors.text} />
          </TouchableOpacity>
        </View>

        {/* Results Count */}
        <View style={[styles.resultsCount, { backgroundColor: colors.card }]}>
          <Text style={[styles.resultsCountText, { color: colors.text }]}>
            {results.length} results found
          </Text>
        </View>

        {/* Loading Indicator */}
        {isLoading && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        )}
      </View>

      {/* Selected Result Card */}
      {selectedResult && (
        <View style={[styles.selectedCard, { backgroundColor: colors.card }]}>
          <View style={styles.selectedCardContent}>
            <View style={styles.selectedCardInfo}>
              <Text style={[styles.selectedCardTitle, { color: colors.text }]}>
                {selectedResult.name}
              </Text>
              <Text style={[styles.selectedCardDistance, { color: colors.textTertiary }]}>
                {selectedResult.distance_km.toFixed(1)}km away • ⭐ {selectedResult.rating}
              </Text>
              <Text style={[styles.selectedCardPrice, { color: colors.primary }]}>
                ৳{selectedResult.price_per_hour || selectedResult.price_per_session}
                {selectedResult.type === 'venue' ? '/hour' : '/session'}
              </Text>
            </View>
            <TouchableOpacity
              style={[styles.selectedCardButton, { backgroundColor: colors.primary }]}
              onPress={() => handleResultPress(selectedResult)}
            >
              <Text style={styles.selectedCardButtonText}>View Details</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* List View Toggle */}
      <TouchableOpacity
        style={[styles.listToggle, { backgroundColor: colors.primary }]}
        onPress={() => router.push({
          pathname: '/search-results',
          params: { 
            query: searchQuery,
            type: searchType,
            mapResults: JSON.stringify(results),
          }
        })}
      >
        <List size={20} color="#FFFFFF" />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    gap: 12,
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  filterButton: {
    padding: 8,
    borderRadius: 8,
  },
  typeSelector: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
  },
  typeButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  typeButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  mapControls: {
    position: 'absolute',
    top: 16,
    right: 16,
    gap: 8,
  },
  controlButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  radiusControls: {
    position: 'absolute',
    bottom: 100,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  radiusButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  radiusText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  resultsCount: {
    position: 'absolute',
    top: 16,
    left: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  resultsCountText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  selectedCard: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  selectedCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    gap: 16,
  },
  selectedCardInfo: {
    flex: 1,
  },
  selectedCardTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  selectedCardDistance: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginBottom: 4,
  },
  selectedCardPrice: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
  },
  selectedCardButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  selectedCardButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  listToggle: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
});
