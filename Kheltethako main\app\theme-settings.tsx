import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { 
  ArrowLeft, 
  Sun, 
  Moon, 
  Monitor,
  Palette,
  Eye,
  Contrast,
  Check
} from 'lucide-react-native';
import { useTheme, Theme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { trackPageView, trackCustomEvent } from '@/services/analyticsService';

export default function ThemeSettingsScreen() {
  const { user } = useAuth();
  const { theme, isDark, colors, setTheme, toggleTheme } = useTheme();

  React.useEffect(() => {
    if (user) {
      trackPageView('/theme-settings', user.id);
    }
  }, [user]);

  const handleThemeChange = async (newTheme: Theme) => {
    setTheme(newTheme);
    
    // Track analytics
    if (user) {
      await trackCustomEvent('theme_changed', user.id, {
        from_theme: theme,
        to_theme: newTheme,
      });
    }
  };

  const themeOptions = [
    {
      value: 'light' as Theme,
      label: 'Light',
      description: 'Clean and bright interface',
      icon: <Sun size={24} color={colors.text} />,
    },
    {
      value: 'dark' as Theme,
      label: 'Dark',
      description: 'Easy on the eyes in low light',
      icon: <Moon size={24} color={colors.text} />,
    },
    {
      value: 'system' as Theme,
      label: 'System',
      description: 'Follows your device settings',
      icon: <Monitor size={24} color={colors.text} />,
    },
  ];

  const renderThemeOption = (option: typeof themeOptions[0]) => (
    <TouchableOpacity
      key={option.value}
      style={[
        styles.themeOption,
        { 
          backgroundColor: colors.card,
          borderColor: colors.border,
        },
        theme === option.value && {
          borderColor: colors.primary,
          backgroundColor: colors.primaryLight,
        }
      ]}
      onPress={() => handleThemeChange(option.value)}
    >
      <View style={styles.themeOptionContent}>
        <View style={styles.themeOptionIcon}>
          {option.icon}
        </View>
        <View style={styles.themeOptionText}>
          <Text style={[
            styles.themeOptionTitle,
            { color: colors.text }
          ]}>
            {option.label}
          </Text>
          <Text style={[
            styles.themeOptionDescription,
            { color: colors.textTertiary }
          ]}>
            {option.description}
          </Text>
        </View>
        {theme === option.value && (
          <View style={[
            styles.themeOptionCheck,
            { backgroundColor: colors.primary }
          ]}>
            <Check size={16} color="#FFFFFF" />
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView 
      style={[styles.container, { backgroundColor: colors.background }]} 
      edges={['top', 'left', 'right']}
    >
      {/* Header */}
      <View style={[
        styles.header,
        { 
          backgroundColor: colors.background,
          borderBottomColor: colors.border,
        }
      ]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: colors.text }]}>
          Theme Settings
        </Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView 
        style={styles.content}
        showsVerticalScrollIndicator={false}
      >
        {/* Current Theme Preview */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Current Theme
          </Text>
          <View style={[
            styles.previewCard,
            { 
              backgroundColor: colors.card,
              borderColor: colors.border,
            }
          ]}>
            <View style={styles.previewHeader}>
              <Palette size={20} color={colors.primary} />
              <Text style={[styles.previewTitle, { color: colors.text }]}>
                {theme.charAt(0).toUpperCase() + theme.slice(1)} Mode
              </Text>
              <Switch
                value={isDark}
                onValueChange={toggleTheme}
                trackColor={{ 
                  false: colors.disabled, 
                  true: colors.primary 
                }}
                thumbColor="#FFFFFF"
              />
            </View>
            <Text style={[styles.previewDescription, { color: colors.textSecondary }]}>
              {isDark 
                ? 'Dark theme is active. Perfect for low-light environments.'
                : 'Light theme is active. Clean and bright interface.'
              }
            </Text>
          </View>
        </View>

        {/* Theme Options */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Choose Theme
          </Text>
          <Text style={[styles.sectionDescription, { color: colors.textTertiary }]}>
            Select your preferred theme or let the system decide
          </Text>
          <View style={styles.themeOptions}>
            {themeOptions.map(renderThemeOption)}
          </View>
        </View>

        {/* Accessibility Features */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Accessibility
          </Text>
          <View style={[
            styles.accessibilityCard,
            { 
              backgroundColor: colors.card,
              borderColor: colors.border,
            }
          ]}>
            <View style={styles.accessibilityItem}>
              <View style={styles.accessibilityIcon}>
                <Contrast size={20} color={colors.text} />
              </View>
              <View style={styles.accessibilityText}>
                <Text style={[styles.accessibilityTitle, { color: colors.text }]}>
                  High Contrast
                </Text>
                <Text style={[styles.accessibilityDescription, { color: colors.textTertiary }]}>
                  Enhance text readability
                </Text>
              </View>
              <Switch
                value={false}
                onValueChange={() => {}}
                trackColor={{ 
                  false: colors.disabled, 
                  true: colors.primary 
                }}
                thumbColor="#FFFFFF"
              />
            </View>

            <View style={[styles.separator, { backgroundColor: colors.border }]} />

            <View style={styles.accessibilityItem}>
              <View style={styles.accessibilityIcon}>
                <Eye size={20} color={colors.text} />
              </View>
              <View style={styles.accessibilityText}>
                <Text style={[styles.accessibilityTitle, { color: colors.text }]}>
                  Reduce Motion
                </Text>
                <Text style={[styles.accessibilityDescription, { color: colors.textTertiary }]}>
                  Minimize animations
                </Text>
              </View>
              <Switch
                value={false}
                onValueChange={() => {}}
                trackColor={{ 
                  false: colors.disabled, 
                  true: colors.primary 
                }}
                thumbColor="#FFFFFF"
              />
            </View>
          </View>
        </View>

        {/* Theme Info */}
        <View style={styles.section}>
          <View style={[
            styles.infoCard,
            { 
              backgroundColor: colors.surface,
              borderColor: colors.borderLight,
            }
          ]}>
            <Text style={[styles.infoTitle, { color: colors.text }]}>
              About Themes
            </Text>
            <Text style={[styles.infoText, { color: colors.textSecondary }]}>
              • Light theme provides a clean, bright interface perfect for daytime use{'\n'}
              • Dark theme reduces eye strain in low-light environments{'\n'}
              • System theme automatically switches based on your device settings{'\n'}
              • Your theme preference is saved and synced across devices
            </Text>
          </View>
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: 'Inter-SemiBold',
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginBottom: 16,
    lineHeight: 20,
  },
  previewCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
  },
  previewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  previewTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    flex: 1,
    marginLeft: 8,
  },
  previewDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
  themeOptions: {
    gap: 12,
  },
  themeOption: {
    borderRadius: 12,
    borderWidth: 2,
    overflow: 'hidden',
  },
  themeOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  themeOptionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  themeOptionText: {
    flex: 1,
  },
  themeOptionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  themeOptionDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  themeOptionCheck: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  accessibilityCard: {
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
  },
  accessibilityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  accessibilityIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  accessibilityText: {
    flex: 1,
  },
  accessibilityTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  accessibilityDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  separator: {
    height: 1,
    marginHorizontal: 16,
  },
  infoCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
  },
  infoTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
  bottomSpacing: {
    height: 32,
  },
});
