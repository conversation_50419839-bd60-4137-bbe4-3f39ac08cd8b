import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Image,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { 
  ArrowLeft, 
  AlertTriangle, 
  Calendar, 
  Clock, 
  MapPin, 
  DollarSign,
  CheckCircle,
  XCircle,
  Info
} from 'lucide-react-native';
import { useAuth } from '@/contexts/AuthContext';
import { getBookingDetails, cancelBooking } from '@/services/supabaseApi';
import { trackCustomEvent } from '@/services/analyticsService';

const CANCELLATION_REASONS = [
  'Schedule conflict',
  'Weather conditions',
  'Personal emergency',
  'Venue/Coach unavailable',
  'Found better option',
  'Financial reasons',
  'Health issues',
  'Other',
];

export default function CancelBookingScreen() {
  const { user } = useAuth();
  const params = useLocalSearchParams();
  const [booking, setBooking] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCancelling, setIsCancelling] = useState(false);
  const [selectedReason, setSelectedReason] = useState('');
  const [customReason, setCustomReason] = useState('');
  const [additionalComments, setAdditionalComments] = useState('');

  useEffect(() => {
    fetchBookingDetails();
  }, []);

  const fetchBookingDetails = async () => {
    try {
      setIsLoading(true);
      if (params.bookingId) {
        const bookingData = await getBookingDetails(params.bookingId as string);
        setBooking(bookingData);
      }
    } catch (error) {
      console.error('Error fetching booking details:', error);
      Alert.alert('Error', 'Failed to load booking details');
      router.back();
    } finally {
      setIsLoading(false);
    }
  };

  const calculateRefundAmount = () => {
    if (!booking) return 0;
    
    const bookingDate = new Date(booking.booking_date);
    const now = new Date();
    const hoursUntilBooking = (bookingDate.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    // Refund policy: 100% if >24h, 50% if >12h, 25% if >6h, 0% if <6h
    if (hoursUntilBooking > 24) {
      return booking.total_amount;
    } else if (hoursUntilBooking > 12) {
      return booking.total_amount * 0.5;
    } else if (hoursUntilBooking > 6) {
      return booking.total_amount * 0.25;
    } else {
      return 0;
    }
  };

  const getRefundPercentage = () => {
    if (!booking) return 0;
    
    const refundAmount = calculateRefundAmount();
    return Math.round((refundAmount / booking.total_amount) * 100);
  };

  const handleCancelBooking = async () => {
    if (!selectedReason && !customReason.trim()) {
      Alert.alert('Reason Required', 'Please select or enter a reason for cancellation');
      return;
    }

    const reason = selectedReason === 'Other' ? customReason : selectedReason;
    const refundAmount = calculateRefundAmount();
    const refundPercentage = getRefundPercentage();

    Alert.alert(
      'Confirm Cancellation',
      `Are you sure you want to cancel this booking?\n\nRefund: ৳${refundAmount} (${refundPercentage}% of total)\n\nThis action cannot be undone.`,
      [
        { text: 'Keep Booking', style: 'cancel' },
        {
          text: 'Cancel Booking',
          style: 'destructive',
          onPress: () => performCancellation(reason),
        },
      ]
    );
  };

  const performCancellation = async (reason: string) => {
    try {
      setIsCancelling(true);

      const success = await cancelBooking(
        parseInt(params.bookingId as string),
        `${reason}${additionalComments ? ` - ${additionalComments}` : ''}`
      );

      if (success) {
        // Track cancellation analytics
        await trackCustomEvent('booking_cancelled', user?.id, {
          booking_id: params.bookingId,
          reason: reason,
          refund_amount: calculateRefundAmount(),
          refund_percentage: getRefundPercentage(),
        });

        Alert.alert(
          'Booking Cancelled',
          `Your booking has been cancelled successfully. ${
            calculateRefundAmount() > 0 
              ? `A refund of ৳${calculateRefundAmount()} will be processed within 3-5 business days.`
              : 'No refund is applicable for this cancellation.'
          }`,
          [
            {
              text: 'OK',
              onPress: () => {
                router.back();
                // Navigate to bookings tab
                router.push('/(tabs)/bookings');
              },
            },
          ]
        );
      } else {
        throw new Error('Cancellation failed');
      }
    } catch (error) {
      console.error('Error cancelling booking:', error);
      Alert.alert('Error', 'Failed to cancel booking. Please try again.');
    } finally {
      setIsCancelling(false);
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color="#111827" />
          </TouchableOpacity>
          <Text style={styles.title}>Cancel Booking</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#EF4444" />
          <Text style={styles.loadingText}>Loading booking details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!booking) {
    return (
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color="#111827" />
          </TouchableOpacity>
          <Text style={styles.title}>Cancel Booking</Text>
        </View>
        <View style={styles.errorContainer}>
          <XCircle size={64} color="#EF4444" />
          <Text style={styles.errorTitle}>Booking Not Found</Text>
          <Text style={styles.errorText}>
            The booking you're trying to cancel could not be found.
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const refundAmount = calculateRefundAmount();
  const refundPercentage = getRefundPercentage();

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color="#111827" />
        </TouchableOpacity>
        <Text style={styles.title}>Cancel Booking</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Warning */}
        <View style={styles.warningContainer}>
          <AlertTriangle size={24} color="#F59E0B" />
          <View style={styles.warningContent}>
            <Text style={styles.warningTitle}>Cancellation Policy</Text>
            <Text style={styles.warningText}>
              Cancellation fees may apply based on timing. Please review the refund details below.
            </Text>
          </View>
        </View>

        {/* Booking Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Booking Details</Text>
          <View style={styles.bookingCard}>
            <Image
              source={{ uri: booking.image_url }}
              style={styles.bookingImage}
            />
            <View style={styles.bookingInfo}>
              <Text style={styles.bookingName}>{booking.name}</Text>
              <View style={styles.bookingDetail}>
                <Calendar size={16} color="#6B7280" />
                <Text style={styles.bookingDetailText}>{booking.date}</Text>
              </View>
              <View style={styles.bookingDetail}>
                <Clock size={16} color="#6B7280" />
                <Text style={styles.bookingDetailText}>{booking.time}</Text>
              </View>
              <View style={styles.bookingDetail}>
                <MapPin size={16} color="#6B7280" />
                <Text style={styles.bookingDetailText}>{booking.location}</Text>
              </View>
              <View style={styles.bookingDetail}>
                <DollarSign size={16} color="#6B7280" />
                <Text style={styles.bookingDetailText}>৳{booking.total_amount}</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Refund Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Refund Information</Text>
          <View style={styles.refundCard}>
            <View style={styles.refundHeader}>
              <Info size={20} color="#3B82F6" />
              <Text style={styles.refundTitle}>Refund Details</Text>
            </View>
            <View style={styles.refundDetails}>
              <View style={styles.refundRow}>
                <Text style={styles.refundLabel}>Original Amount:</Text>
                <Text style={styles.refundValue}>৳{booking.total_amount}</Text>
              </View>
              <View style={styles.refundRow}>
                <Text style={styles.refundLabel}>Refund Amount:</Text>
                <Text style={[styles.refundValue, styles.refundAmount]}>
                  ৳{refundAmount} ({refundPercentage}%)
                </Text>
              </View>
              <View style={styles.refundRow}>
                <Text style={styles.refundLabel}>Processing Time:</Text>
                <Text style={styles.refundValue}>3-5 business days</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Cancellation Reason */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Reason for Cancellation</Text>
          <View style={styles.reasonsContainer}>
            {CANCELLATION_REASONS.map((reason) => (
              <TouchableOpacity
                key={reason}
                style={[
                  styles.reasonButton,
                  selectedReason === reason && styles.reasonButtonSelected
                ]}
                onPress={() => setSelectedReason(reason)}
              >
                <Text style={[
                  styles.reasonText,
                  selectedReason === reason && styles.reasonTextSelected
                ]}>
                  {reason}
                </Text>
                {selectedReason === reason && (
                  <CheckCircle size={16} color="#FFFFFF" />
                )}
              </TouchableOpacity>
            ))}
          </View>

          {selectedReason === 'Other' && (
            <TextInput
              style={styles.customReasonInput}
              placeholder="Please specify your reason..."
              placeholderTextColor="#9CA3AF"
              value={customReason}
              onChangeText={setCustomReason}
              multiline
              numberOfLines={3}
            />
          )}

          <Text style={styles.commentsLabel}>Additional Comments (Optional)</Text>
          <TextInput
            style={styles.commentsInput}
            placeholder="Any additional information..."
            placeholderTextColor="#9CA3AF"
            value={additionalComments}
            onChangeText={setAdditionalComments}
            multiline
            numberOfLines={3}
          />
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Footer */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.keepButton}
          onPress={() => router.back()}
        >
          <Text style={styles.keepButtonText}>Keep Booking</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.cancelButton, isCancelling && styles.cancelButtonDisabled]}
          onPress={handleCancelBooking}
          disabled={isCancelling}
        >
          {isCancelling ? (
            <ActivityIndicator color="#FFFFFF" size="small" />
          ) : (
            <Text style={styles.cancelButtonText}>Cancel Booking</Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    fontFamily: 'Inter-SemiBold',
    flex: 1,
    textAlign: 'center',
    marginRight: 32,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#FFFBEB',
    borderRadius: 12,
    padding: 16,
    marginTop: 20,
    borderWidth: 1,
    borderColor: '#FDE68A',
  },
  warningContent: {
    flex: 1,
    marginLeft: 12,
  },
  warningTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#92400E',
    marginBottom: 4,
  },
  warningText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#B45309',
    lineHeight: 20,
  },
  section: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 16,
  },
  bookingCard: {
    flexDirection: 'row',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  bookingImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 16,
  },
  bookingInfo: {
    flex: 1,
  },
  bookingName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 8,
  },
  bookingDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  bookingDetailText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 8,
  },
  refundCard: {
    backgroundColor: '#F0F9FF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#BAE6FD',
  },
  refundHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  refundTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1E40AF',
    marginLeft: 8,
  },
  refundDetails: {
    gap: 8,
  },
  refundRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  refundLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#374151',
  },
  refundValue: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
  },
  refundAmount: {
    color: '#059669',
    fontFamily: 'Inter-SemiBold',
  },
  reasonsContainer: {
    gap: 8,
    marginBottom: 16,
  },
  reasonButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  reasonButtonSelected: {
    backgroundColor: '#EF4444',
    borderColor: '#EF4444',
  },
  reasonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
  },
  reasonTextSelected: {
    color: '#FFFFFF',
  },
  customReasonInput: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    marginBottom: 16,
    textAlignVertical: 'top',
  },
  commentsLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: 8,
  },
  commentsInput: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    textAlignVertical: 'top',
  },
  bottomSpacing: {
    height: 100,
  },
  footer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
    gap: 12,
  },
  keepButton: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  keepButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#EF4444',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  cancelButtonDisabled: {
    opacity: 0.6,
  },
  cancelButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});
