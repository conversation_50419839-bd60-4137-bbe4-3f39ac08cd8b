import { supabase } from '@/lib/supabase';

// Types
export interface User {
  id: string;
  email: string;
  full_name: string;
  phone?: string;
  location?: string;
  avatar_url?: string;
  member_since?: string;
  is_active?: boolean;
}

export interface Venue {
  id: number;
  name: string;
  slug: string;
  description?: string;
  category_id?: number;
  sport_id?: number;
  address: string;
  city: string;
  area?: string;
  latitude?: number;
  longitude?: number;
  price_per_hour: number;
  currency?: string;
  capacity?: number;
  surface_type?: string;
  is_indoor?: boolean;
  opening_time?: string;
  closing_time?: string;
  amenities?: string[];
  contact_phone?: string;
  contact_email?: string;
  manager_name?: string;
  images?: string[];
  average_rating?: number;
  total_reviews?: number;
  is_active?: boolean;
  is_verified?: boolean;
  created_at?: string;
  updated_at?: string;
  // Computed fields
  location?: string;
  rating?: number;
  reviews_count?: number;
  image_url?: string;
  features?: string[];
}

export interface Coach {
  id: number;
  user_id?: string;
  bio?: string;
  experience_years?: number;
  sport_id?: number;
  specialties?: string[];
  certifications?: string[];
  hourly_rate: number;
  currency?: string;
  available_days?: string[];
  available_times?: any;
  city?: string;
  area?: string;
  travel_radius?: number;
  languages?: string[];
  average_rating?: number;
  total_reviews?: number;
  avg_response_time?: number;
  is_active?: boolean;
  is_verified?: boolean;
  created_at?: string;
  updated_at?: string;
  // Computed fields from user table
  name?: string;
  image_url?: string;
  specialty?: string;
  rating?: number;
  reviews_count?: number;
  price_per_session?: number;
}

export interface Booking {
  id: number;
  booking_number: string;
  user_id?: string;
  venue_id?: number;
  booking_date: string;
  start_time: string;
  end_time: string;
  duration_hours: number;
  total_players?: number;
  special_requests?: string;
  price_per_hour: number;
  total_amount: number;
  currency?: string;
  status?: string;
  payment_status?: string;
  payment_method?: string;
  payment_transaction_id?: string;
  paid_at?: string;
  cancelled_at?: string;
  cancellation_reason?: string;
  refund_amount?: number;
  refunded_at?: string;
  created_at?: string;
  updated_at?: string;
  // Computed fields
  type?: 'venue' | 'coach';
  name?: string;
  location?: string;
  date?: string;
  time?: string;
  price?: number;
  image_url?: string;
}

export interface Sport {
  id: number;
  name: string;
  icon?: string;
  description?: string;
  is_active?: boolean;
  created_at?: string;
}

export interface Review {
  id: number;
  user_id?: string;
  venue_id?: number;
  coach_id?: number;
  booking_id?: number;
  rating: number;
  title?: string;
  comment: string;
  photos?: string[];
  is_approved?: boolean;
  is_featured?: boolean;
  created_at?: string;
  updated_at?: string;
  // Computed fields
  user_name?: string;
  user_avatar?: string;
}

export interface Notification {
  id: number;
  user_id?: string;
  title: string;
  message: string;
  type?: string;
  is_read?: boolean;
  action_url?: string;
  created_at?: string;
}

// API Functions

// Mock data based on database structure
const mockSports: Sport[] = [
  { id: 1, name: 'Football', icon: 'football', description: 'Association football/soccer', is_active: true },
  { id: 2, name: 'Cricket', icon: 'cricket', description: 'Cricket matches and practice', is_active: true },
  { id: 3, name: 'Basketball', icon: 'basketball', description: 'Indoor and outdoor basketball', is_active: true },
  { id: 4, name: 'Tennis', icon: 'tennis', description: 'Tennis courts for singles and doubles', is_active: true },
  { id: 5, name: 'Badminton', icon: 'badminton', description: 'Indoor badminton courts', is_active: true },
  { id: 6, name: 'Swimming', icon: 'swimming', description: 'Swimming pools and aquatic sports', is_active: true },
  { id: 7, name: 'Volleyball', icon: 'volleyball', description: 'Indoor and beach volleyball', is_active: true },
  { id: 8, name: 'Table Tennis', icon: 'table-tennis', description: 'Table tennis/ping pong', is_active: true },
];

const mockVenues: Venue[] = [
  {
    id: 1,
    name: 'Green Valley Football Club',
    slug: 'green-valley-football-club',
    description: 'Green Valley Football Club is a premium sports facility featuring a professional-grade artificial turf field.',
    sport_id: 1,
    address: 'House 123, Road 45, Dhanmondi, Dhaka 1205',
    city: 'Dhaka',
    area: 'Dhanmondi',
    price_per_hour: 2000,
    currency: 'BDT',
    capacity: 22,
    surface_type: 'Professional Artificial Turf',
    is_indoor: false,
    opening_time: '06:00:00',
    closing_time: '23:00:00',
    amenities: ['Free Parking', 'Free WiFi', 'Security', 'Changing Rooms', 'CCTV', 'First Aid'],
    contact_phone: '+880 1234-567890',
    contact_email: '<EMAIL>',
    manager_name: 'Rahman Ahmed',
    images: ['https://images.unsplash.com/photo-1459865264687-595d652de67e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'],
    average_rating: 4.5,
    total_reviews: 2,
    is_active: true,
    is_verified: true,
    location: 'Dhanmondi, Dhaka',
    rating: 4.5,
    reviews_count: 2,
    image_url: 'https://images.unsplash.com/photo-1459865264687-595d652de67e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    features: ['Free Parking', 'Free WiFi', 'Security', 'Changing Rooms'],
  },
  {
    id: 2,
    name: 'Cricket Champions Ground',
    slug: 'cricket-champions-ground',
    description: 'State-of-the-art cricket facility with professional pitch and modern pavilion.',
    sport_id: 2,
    address: 'Plot 78, Gulshan Avenue, Gulshan, Dhaka 1212',
    city: 'Dhaka',
    area: 'Gulshan',
    price_per_hour: 3000,
    currency: 'BDT',
    capacity: 22,
    surface_type: 'Professional Cricket Pitch',
    is_indoor: false,
    opening_time: '05:00:00',
    closing_time: '22:00:00',
    amenities: ['Professional Pitch', 'Pavilion', 'Equipment', 'Scoreboard', 'Parking'],
    contact_phone: '+880 1234-567891',
    contact_email: '<EMAIL>',
    manager_name: 'Karim Hassan',
    images: ['https://images.unsplash.com/photo-1540747913346-19e32dc3e97e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'],
    average_rating: 5.0,
    total_reviews: 1,
    is_active: true,
    is_verified: true,
    location: 'Gulshan, Dhaka',
    rating: 5.0,
    reviews_count: 1,
    image_url: 'https://images.unsplash.com/photo-1540747913346-19e32dc3e97e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    features: ['Professional Pitch', 'Pavilion', 'Equipment', 'Scoreboard'],
  },
  {
    id: 3,
    name: 'Basketball Arena Pro',
    slug: 'basketball-arena-pro',
    description: 'Indoor basketball court with professional flooring, air conditioning, and sound system.',
    sport_id: 3,
    address: 'Building 25, Banani Commercial Area, Banani, Dhaka 1213',
    city: 'Dhaka',
    area: 'Banani',
    price_per_hour: 1500,
    currency: 'BDT',
    capacity: 10,
    surface_type: 'Professional Basketball Court',
    is_indoor: true,
    opening_time: '07:00:00',
    closing_time: '24:00:00',
    amenities: ['Indoor Court', 'AC', 'Sound System', 'Changing Rooms', 'Equipment Storage'],
    contact_phone: '+880 1234-567892',
    contact_email: '<EMAIL>',
    manager_name: 'Fatima Khatun',
    images: ['https://images.unsplash.com/photo-1546519638-68e109498ffc?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'],
    average_rating: 4.0,
    total_reviews: 1,
    is_active: true,
    is_verified: true,
    location: 'Banani, Dhaka',
    rating: 4.0,
    reviews_count: 1,
    image_url: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    features: ['Indoor Court', 'AC', 'Sound System', 'Changing Rooms'],
  },
];

const mockCoaches: Coach[] = [
  {
    id: 1,
    user_id: 'coach-1',
    bio: 'Professional football coach with 8 years of experience. Specialized in youth training and advanced techniques.',
    experience_years: 8,
    sport_id: 1,
    specialties: ['Youth Training', 'Advanced Techniques', 'Team Strategy'],
    certifications: [],
    hourly_rate: 1500,
    currency: 'BDT',
    available_days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
    city: 'Dhaka',
    area: 'Dhanmondi',
    travel_radius: 5,
    languages: ['Bengali', 'English'],
    average_rating: 4.8,
    total_reviews: 15,
    avg_response_time: 24,
    is_active: true,
    is_verified: true,
    name: 'Rafiq Ahmed',
    image_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
    specialty: 'Football',
    rating: 4.8,
    reviews_count: 15,
    price_per_session: 1500,
  },
  {
    id: 2,
    user_id: 'coach-2',
    bio: 'Expert cricket coach focusing on batting techniques and womens cricket development.',
    experience_years: 6,
    sport_id: 2,
    specialties: ['Batting Techniques', 'Womens Cricket', 'Match Strategy'],
    certifications: [],
    hourly_rate: 2000,
    currency: 'BDT',
    available_days: ['tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
    city: 'Dhaka',
    area: 'Gulshan',
    travel_radius: 5,
    languages: ['Bengali', 'English', 'Hindi'],
    average_rating: 4.9,
    total_reviews: 12,
    avg_response_time: 24,
    is_active: true,
    is_verified: true,
    name: 'Fatima Khan',
    image_url: 'https://images.unsplash.com/photo-1494790108755-2616b612b093?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
    specialty: 'Cricket',
    rating: 4.9,
    reviews_count: 12,
    price_per_session: 2000,
  },
];

const mockBookings: Booking[] = [
  {
    id: 1,
    booking_number: 'BK001',
    user_id: 'mock-user-id',
    venue_id: 1,
    booking_date: '2025-02-15',
    start_time: '14:00',
    end_time: '16:00',
    duration_hours: 2,
    total_players: 10,
    special_requests: 'Need equipment rental',
    price_per_hour: 2000,
    total_amount: 4000,
    currency: 'BDT',
    status: 'confirmed',
    payment_status: 'paid',
    payment_method: 'bkash',
    type: 'venue',
    name: 'Green Valley Football Club',
    location: 'Dhanmondi, Dhaka',
    date: '2025-02-15',
    time: '14:00 - 16:00',
    price: 4000,
    image_url: 'https://images.unsplash.com/photo-1459865264687-595d652de67e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
  },
  {
    id: 2,
    booking_number: 'BK002',
    user_id: 'mock-user-id',
    venue_id: 2,
    booking_date: '2025-01-20',
    start_time: '10:00',
    end_time: '12:00',
    duration_hours: 2,
    total_players: 15,
    price_per_hour: 3000,
    total_amount: 6000,
    currency: 'BDT',
    status: 'completed',
    payment_status: 'paid',
    payment_method: 'card',
    type: 'venue',
    name: 'Cricket Champions Ground',
    location: 'Gulshan, Dhaka',
    date: '2025-01-20',
    time: '10:00 - 12:00',
    price: 6000,
    image_url: 'https://images.unsplash.com/photo-1540747913346-19e32dc3e97e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
  },
  {
    id: 3,
    booking_number: 'BK003',
    user_id: 'mock-user-id',
    venue_id: 3,
    booking_date: '2025-01-10',
    start_time: '18:00',
    end_time: '20:00',
    duration_hours: 2,
    total_players: 8,
    price_per_hour: 1500,
    total_amount: 3000,
    currency: 'BDT',
    status: 'cancelled',
    payment_status: 'refunded',
    payment_method: 'nagad',
    cancellation_reason: 'Weather conditions',
    type: 'venue',
    name: 'Basketball Arena Pro',
    location: 'Banani, Dhaka',
    date: '2025-01-10',
    time: '18:00 - 20:00',
    price: 3000,
    image_url: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
  },
];

const mockReviews: Review[] = [
  {
    id: 1,
    user_id: 'user-1',
    venue_id: 1,
    rating: 5,
    title: 'Excellent facility!',
    comment: 'Great football ground with excellent facilities. The artificial turf is in perfect condition and the changing rooms are clean.',
    is_approved: true,
    is_featured: true,
    created_at: '2025-01-15T10:30:00Z',
    user_name: 'Ahmed Rahman',
    user_avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
  },
  {
    id: 2,
    user_id: 'user-2',
    venue_id: 1,
    rating: 4,
    title: 'Good value for money',
    comment: 'Nice ground, good facilities. Only issue was parking was a bit crowded during peak hours.',
    is_approved: true,
    is_featured: false,
    created_at: '2025-01-10T14:20:00Z',
    user_name: 'Fatima Khan',
    user_avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b093?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
  },
  {
    id: 3,
    user_id: 'user-3',
    venue_id: 2,
    rating: 5,
    title: 'Perfect cricket ground',
    comment: 'Professional quality pitch, excellent pavilion facilities. Highly recommended for serious cricket matches.',
    is_approved: true,
    is_featured: true,
    created_at: '2025-01-08T16:45:00Z',
    user_name: 'Karim Hassan',
    user_avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
  },
  {
    id: 4,
    user_id: 'user-4',
    venue_id: 3,
    rating: 4,
    title: 'Great indoor court',
    comment: 'Clean indoor basketball court with good air conditioning. Perfect for year-round play.',
    is_approved: true,
    is_featured: false,
    created_at: '2025-01-05T12:15:00Z',
    user_name: 'Sarah Ahmed',
    user_avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
  },
  {
    id: 5,
    user_id: 'user-5',
    coach_id: 1,
    rating: 5,
    title: 'Excellent coach!',
    comment: 'Rafiq is an amazing football coach. Very patient and knowledgeable. My skills improved significantly.',
    is_approved: true,
    is_featured: true,
    created_at: '2025-01-12T09:30:00Z',
    user_name: 'Mahmud Ali',
    user_avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
  },
  {
    id: 6,
    user_id: 'user-6',
    coach_id: 2,
    rating: 5,
    title: 'Best cricket coach',
    comment: 'Fatima is an excellent cricket coach. Her batting techniques are top-notch and she explains everything clearly.',
    is_approved: true,
    is_featured: true,
    created_at: '2025-01-07T11:20:00Z',
    user_name: 'Rashid Khan',
    user_avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
  },
];

const mockFavorites = [
  { id: 1, user_id: 'mock-user-id', venue_id: 1, coach_id: null },
  { id: 2, user_id: 'mock-user-id', venue_id: null, coach_id: 1 },
];

const mockNotifications: Notification[] = [
  {
    id: 1,
    user_id: 'mock-user-id',
    title: 'Booking Confirmed',
    message: 'Your booking at Green Valley Football Club has been confirmed for Feb 15, 2025.',
    type: 'booking',
    is_read: false,
    action_url: '/booking-details/1',
    created_at: '2025-01-30T10:30:00Z',
  },
  {
    id: 2,
    user_id: 'mock-user-id',
    title: 'Payment Successful',
    message: 'Payment of ৳4,000 has been processed successfully.',
    type: 'payment',
    is_read: false,
    created_at: '2025-01-30T10:25:00Z',
  },
  {
    id: 3,
    user_id: 'mock-user-id',
    title: 'New Coach Available',
    message: 'A new cricket coach is now available in your area.',
    type: 'promotion',
    is_read: true,
    created_at: '2025-01-29T14:20:00Z',
  },
];

// Sports
export const getSports = async (): Promise<Sport[]> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockSports;
  } catch (error) {
    console.error('Error fetching sports:', error);
    return [];
  }
};

// Venues
export const getPopularVenues = async (categoryId?: string | null): Promise<Venue[]> => {
  try {
    let query = supabase
      .from('venues')
      .select(`
        *,
        sports (
          name,
          icon
        )
      `)
      .eq('is_active', true)
      .eq('is_verified', true);

    if (categoryId) {
      query = query.eq('sport_id', parseInt(categoryId));
    }

    const { data, error } = await query
      .order('average_rating', { ascending: false })
      .limit(10);

    if (error) {
      console.error('Error fetching popular venues:', error);
      return [];
    }

    // Transform data to match expected format
    const venues: Venue[] = data?.map(venue => ({
      id: venue.id,
      name: venue.name,
      slug: venue.slug,
      description: venue.description,
      sport_id: venue.sport_id,
      address: venue.address,
      city: venue.city,
      area: venue.area,
      latitude: parseFloat(venue.latitude) || 0,
      longitude: parseFloat(venue.longitude) || 0,
      price_per_hour: parseFloat(venue.price_per_hour),
      currency: venue.currency,
      capacity: venue.capacity,
      surface_type: venue.surface_type,
      is_indoor: venue.is_indoor,
      opening_time: venue.opening_time,
      closing_time: venue.closing_time,
      amenities: venue.amenities || [],
      contact_phone: venue.contact_phone,
      contact_email: venue.contact_email,
      manager_name: venue.manager_name,
      images: venue.images || ['https://images.unsplash.com/photo-1459865264687-595d652de67e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'],
      average_rating: parseFloat(venue.average_rating) || 0,
      total_reviews: venue.total_reviews || 0,
      is_active: venue.is_active,
      is_verified: venue.is_verified,
      sport_name: venue.sports?.name || 'Unknown',
      sport_icon: venue.sports?.icon || '⚽',
    })) || [];

    return venues;
  } catch (error) {
    console.error('Error fetching popular venues:', error);
    return [];
  }
};

// Coaches
export const getRecommendedCoaches = async (categoryId?: string | null): Promise<Coach[]> => {
  try {
    let query = supabase
      .from('coaches')
      .select(`
        *,
        users (
          full_name,
          email,
          phone,
          avatar_url
        ),
        sports (
          name,
          icon
        )
      `)
      .eq('is_active', true)
      .eq('is_verified', true);

    if (categoryId) {
      query = query.eq('sport_id', parseInt(categoryId));
    }

    const { data, error } = await query
      .order('average_rating', { ascending: false })
      .limit(10);

    if (error) {
      console.error('Error fetching recommended coaches:', error);
      return [];
    }

    // Transform data to match expected format
    const coaches: Coach[] = data?.map(coach => ({
      id: coach.id,
      user_id: coach.user_id,
      sport_id: coach.sport_id,
      bio: coach.bio,
      experience_years: coach.experience_years,
      city: coach.city,
      area: coach.area,
      hourly_rate: parseFloat(coach.hourly_rate),
      currency: coach.currency,
      specialties: coach.specialties || [],
      certifications: coach.certifications || [],
      languages: coach.languages || [],
      average_rating: parseFloat(coach.average_rating) || 0,
      total_reviews: coach.total_reviews || 0,
      is_active: coach.is_active,
      is_verified: coach.is_verified,
      name: coach.users?.full_name || 'Unknown Coach',
      email: coach.users?.email,
      phone: coach.users?.phone,
      avatar_url: coach.users?.avatar_url || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      sport_name: coach.sports?.name || 'Unknown',
      sport_icon: coach.sports?.icon || '🏃',
    })) || [];

    return coaches;
  } catch (error) {
    console.error('Error fetching recommended coaches:', error);
    return [];
  }
};

// Search venues
export const searchVenues = async (
  query: string,
  filters: string[],
  sortBy: string
): Promise<Venue[]> => {
  try {
    let supabaseQuery = supabase
      .from('venues')
      .select(`
        *,
        sports (
          name,
          icon
        )
      `)
      .eq('is_active', true)
      .eq('is_verified', true);

    // Apply search query
    if (query) {
      supabaseQuery = supabaseQuery.or(`name.ilike.%${query}%,city.ilike.%${query}%,area.ilike.%${query}%`);
    }

    // Apply sport filters
    if (filters.length > 0) {
      const { data: sports } = await supabase
        .from('sports')
        .select('id')
        .in('name', filters);

      if (sports && sports.length > 0) {
        const sportIds = sports.map(sport => sport.id);
        supabaseQuery = supabaseQuery.in('sport_id', sportIds);
      }
    }

    // Apply sorting
    if (sortBy === 'price_low') {
      supabaseQuery = supabaseQuery.order('price_per_hour', { ascending: true });
    } else if (sortBy === 'price_high') {
      supabaseQuery = supabaseQuery.order('price_per_hour', { ascending: false });
    } else if (sortBy === 'rating') {
      supabaseQuery = supabaseQuery.order('average_rating', { ascending: false });
    } else {
      supabaseQuery = supabaseQuery.order('average_rating', { ascending: false });
    }

    const { data, error } = await supabaseQuery;

    if (error) {
      console.error('Error searching venues:', error);
      return [];
    }

    // Transform data to match expected format
    const venues: Venue[] = data?.map(venue => ({
      id: venue.id,
      name: venue.name,
      slug: venue.slug,
      description: venue.description,
      sport_id: venue.sport_id,
      address: venue.address,
      city: venue.city,
      area: venue.area,
      latitude: parseFloat(venue.latitude) || 0,
      longitude: parseFloat(venue.longitude) || 0,
      price_per_hour: parseFloat(venue.price_per_hour),
      currency: venue.currency,
      capacity: venue.capacity,
      surface_type: venue.surface_type,
      is_indoor: venue.is_indoor,
      opening_time: venue.opening_time,
      closing_time: venue.closing_time,
      amenities: venue.amenities || [],
      contact_phone: venue.contact_phone,
      contact_email: venue.contact_email,
      manager_name: venue.manager_name,
      images: venue.images || ['https://images.unsplash.com/photo-1459865264687-595d652de67e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'],
      average_rating: parseFloat(venue.average_rating) || 0,
      total_reviews: venue.total_reviews || 0,
      is_active: venue.is_active,
      is_verified: venue.is_verified,
      sport_name: venue.sports?.name || 'Unknown',
      sport_icon: venue.sports?.icon || '⚽',
    })) || [];

    return venues;
  } catch (error) {
    console.error('Error searching venues:', error);
    return [];
  }
};

// Get venue details
export const getVenueDetails = async (id: string | string[]): Promise<Venue | null> => {
  try {
    const venueId = typeof id === 'string' ? parseInt(id) : parseInt(id[0]);

    const { data, error } = await supabase
      .from('venues')
      .select(`
        *,
        sports (
          name,
          icon
        )
      `)
      .eq('id', venueId)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching venue details:', error);
      return null;
    }

    if (!data) return null;

    // Transform data to match expected format
    const venue: Venue = {
      id: data.id,
      name: data.name,
      slug: data.slug,
      description: data.description,
      sport_id: data.sport_id,
      address: data.address,
      city: data.city,
      area: data.area,
      latitude: parseFloat(data.latitude) || 0,
      longitude: parseFloat(data.longitude) || 0,
      price_per_hour: parseFloat(data.price_per_hour),
      currency: data.currency,
      capacity: data.capacity,
      surface_type: data.surface_type,
      is_indoor: data.is_indoor,
      opening_time: data.opening_time,
      closing_time: data.closing_time,
      amenities: data.amenities || [],
      contact_phone: data.contact_phone,
      contact_email: data.contact_email,
      manager_name: data.manager_name,
      images: data.images || ['https://images.unsplash.com/photo-1459865264687-595d652de67e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'],
      average_rating: parseFloat(data.average_rating) || 0,
      total_reviews: data.total_reviews || 0,
      is_active: data.is_active,
      is_verified: data.is_verified,
      sport_name: data.sports?.name || 'Unknown',
      sport_icon: data.sports?.icon || '⚽',
    };

    return venue;
  } catch (error) {
    console.error('Error fetching venue details:', error);
    return null;
  }
};

// Get coach details
export const getCoachDetails = async (id: string | string[]): Promise<Coach | null> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const coachId = typeof id === 'string' ? parseInt(id) : parseInt(id[0]);
    const coach = mockCoaches.find(c => c.id === coachId);

    return coach || null;
  } catch (error) {
    console.error('Error fetching coach details:', error);
    return null;
  }
};

// Get user bookings
export const getUserBookings = async (userId: string, type: string): Promise<Booking[]> => {
  try {
    const currentDate = new Date().toISOString().split('T')[0];

    let query = supabase
      .from('bookings')
      .select(`
        *,
        venues (
          name,
          address
        ),
        coaches (
          user_id,
          users (
            full_name
          )
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    // Apply filters based on type
    if (type === 'upcoming') {
      query = query
        .gte('booking_date', currentDate)
        .neq('status', 'cancelled');
    } else if (type === 'past') {
      query = query
        .or(`booking_date.lt.${currentDate},status.eq.completed`);
    } else if (type === 'cancelled') {
      query = query.eq('status', 'cancelled');
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching user bookings:', error);
      return [];
    }

    // Transform data to match expected format
    const formattedBookings: Booking[] = data?.map(booking => ({
      id: booking.id,
      booking_number: booking.booking_number,
      user_id: booking.user_id,
      venue_id: booking.venue_id,
      booking_date: booking.booking_date,
      start_time: booking.start_time,
      end_time: booking.end_time,
      duration_hours: booking.duration_hours,
      total_players: booking.player_count,
      price_per_hour: booking.price_per_hour,
      total_amount: booking.total_amount,
      currency: booking.currency,
      status: booking.status,
      payment_status: booking.payment_status,
      payment_method: booking.payment_method,
      created_at: booking.created_at,
      // Computed fields
      type: booking.venue_id ? 'venue' : 'coach',
      name: booking.venue_id
        ? booking.venues?.name
        : booking.coaches?.users?.full_name,
      location: booking.venue_id
        ? booking.venues?.address
        : 'Coach Session',
      date: booking.booking_date,
      time: `${booking.start_time} - ${booking.end_time}`,
      price: booking.total_amount,
      image_url: 'https://images.unsplash.com/photo-1459865264687-595d652de67e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    })) || [];

    return formattedBookings;
  } catch (error) {
    console.error('Error fetching user bookings:', error);
    return [];
  }
};

// Get booking details
export const getBookingDetails = async (id: string): Promise<Booking | null> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const bookingId = parseInt(id);
    const booking = mockBookings.find(b => b.id === bookingId);

    return booking || null;
  } catch (error) {
    console.error('Error fetching booking details:', error);
    return null;
  }
};



// Cancel booking
export const cancelBooking = async (bookingId: number, reason?: string): Promise<boolean> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const bookingIndex = mockBookings.findIndex(b => b.id === bookingId);
    if (bookingIndex !== -1) {
      mockBookings[bookingIndex] = {
        ...mockBookings[bookingIndex],
        status: 'cancelled',
        cancelled_at: new Date().toISOString(),
        cancellation_reason: reason,
      };
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error cancelling booking:', error);
    return false;
  }
};

// Reviews
export const getVenueReviews = async (venueId: number): Promise<Review[]> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    return mockReviews
      .filter(review => review.venue_id === venueId && review.is_approved)
      .sort((a, b) => new Date(b.created_at!).getTime() - new Date(a.created_at!).getTime());
  } catch (error) {
    console.error('Error fetching venue reviews:', error);
    return [];
  }
};

export const getCoachReviews = async (coachId: number): Promise<Review[]> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    return mockReviews
      .filter(review => review.coach_id === coachId && review.is_approved)
      .sort((a, b) => new Date(b.created_at!).getTime() - new Date(a.created_at!).getTime());
  } catch (error) {
    console.error('Error fetching coach reviews:', error);
    return [];
  }
};

// Create review
export const createReview = async (reviewData: {
  user_id: string;
  venue_id?: number;
  coach_id?: number;
  booking_id?: number;
  rating: number;
  title?: string;
  comment: string;
  photos?: string[];
}): Promise<Review | null> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const newId = Math.max(...mockReviews.map(r => r.id)) + 1;
    const newReview: Review = {
      ...reviewData,
      id: newId,
      is_approved: true,
      is_featured: false,
      created_at: new Date().toISOString(),
      user_name: 'Current User',
      user_avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
    };

    mockReviews.push(newReview);
    return newReview;
  } catch (error) {
    console.error('Error creating review:', error);
    return null;
  }
};

// Favorites
export const getUserFavorites = async (userId: string, type: 'venues' | 'coaches'): Promise<any[]> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const userFavorites = mockFavorites.filter(fav => fav.user_id === userId);

    if (type === 'venues') {
      return userFavorites
        .filter(fav => fav.venue_id)
        .map(fav => {
          const venue = mockVenues.find(v => v.id === fav.venue_id);
          return venue ? { ...venue, type: 'venue' } : null;
        })
        .filter(Boolean);
    } else {
      return userFavorites
        .filter(fav => fav.coach_id)
        .map(fav => {
          const coach = mockCoaches.find(c => c.id === fav.coach_id);
          return coach ? { ...coach, type: 'coach' } : null;
        })
        .filter(Boolean);
    }
  } catch (error) {
    console.error('Error fetching user favorites:', error);
    return [];
  }
};

export const addToFavorites = async (userId: string, venueId?: number, coachId?: number): Promise<boolean> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const newId = Math.max(...mockFavorites.map(f => f.id)) + 1;
    mockFavorites.push({
      id: newId,
      user_id: userId,
      venue_id: venueId || null,
      coach_id: coachId || null,
    });

    return true;
  } catch (error) {
    console.error('Error adding to favorites:', error);
    return false;
  }
};

export const removeFromFavorites = async (userId: string, venueId?: number, coachId?: number): Promise<boolean> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const index = mockFavorites.findIndex(fav =>
      fav.user_id === userId &&
      (venueId ? fav.venue_id === venueId : fav.coach_id === coachId)
    );

    if (index !== -1) {
      mockFavorites.splice(index, 1);
      return true;
    }

    return false;
  } catch (error) {
    console.error('Error removing from favorites:', error);
    return false;
  }
};

// Notifications
export const getUserNotifications = async (userId: string, isRead?: boolean): Promise<Notification[]> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    let filteredNotifications = mockNotifications.filter(notif => notif.user_id === userId);

    if (isRead !== undefined) {
      filteredNotifications = filteredNotifications.filter(notif => notif.is_read === isRead);
    }

    return filteredNotifications.sort((a, b) =>
      new Date(b.created_at!).getTime() - new Date(a.created_at!).getTime()
    );
  } catch (error) {
    console.error('Error fetching user notifications:', error);
    return [];
  }
};

export const markNotificationAsRead = async (notificationId: number): Promise<boolean> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    const notification = mockNotifications.find(n => n.id === notificationId);
    if (notification) {
      notification.is_read = true;
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return false;
  }
};

export const markAllNotificationsAsRead = async (userId: string): Promise<boolean> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    mockNotifications
      .filter(n => n.user_id === userId && !n.is_read)
      .forEach(n => n.is_read = true);

    return true;
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    return false;
  }
};

export const deleteNotification = async (notificationId: number): Promise<boolean> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    const index = mockNotifications.findIndex(n => n.id === notificationId);
    if (index !== -1) {
      mockNotifications.splice(index, 1);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error deleting notification:', error);
    return false;
  }
};

// User management
export const updateUserProfile = async (userId: string, profileData: {
  full_name?: string;
  phone?: string;
  location?: string;
  avatar_url?: string;
}): Promise<boolean> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // In a real implementation, this would update the user in the database
    console.log('Updating user profile:', userId, profileData);
    return true;
  } catch (error) {
    console.error('Error updating user profile:', error);
    return false;
  }
};

export const getUserProfile = async (userId: string): Promise<User | null> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Return mock user profile
    return {
      id: userId,
      email: '<EMAIL>',
      full_name: 'Demo User',
      phone: '+880 1234-567890',
      location: 'Dhaka, Bangladesh',
      avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      member_since: '2024-01-01',
      is_active: true,
    };
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return null;
  }
};

// Check time slot availability
export const checkTimeSlotAvailability = async ({
  venue_id,
  coach_id,
  booking_date,
  start_time,
  end_time,
}: {
  venue_id?: number;
  coach_id?: number;
  booking_date: string;
  start_time: string;
  end_time: string;
}) => {
  try {
    console.log('🔍 Checking availability for:', {
      venue_id,
      coach_id,
      booking_date,
      start_time,
      end_time,
    });

    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === 'development';

    let query = supabase
      .from('bookings')
      .select('id')
      .eq('booking_date', booking_date)
      .in('status', ['confirmed', 'pending'])
      .or(`start_time.lt.${end_time},end_time.gt.${start_time}`);

    if (venue_id) {
      query = query.eq('venue_id', venue_id);
    } else if (coach_id) {
      query = query.eq('coach_id', coach_id);
    }

    const { data, error } = await query;

    if (error) {
      console.warn('Database error during availability check:', error.message);

      // In development, allow booking if table doesn't exist
      if (isDevelopment && (error.message.includes('relation') || error.message.includes('table'))) {
        console.log('🎯 Development mode: Allowing booking due to missing table');
        return true;
      }

      // In production, be more strict about errors
      console.error('❌ Production availability check failed:', error);
      return false;
    }

    // If no conflicting bookings found, slot is available
    const isAvailable = data.length === 0;
    console.log('✅ Availability check result:', isAvailable);

    if (!isAvailable) {
      console.log('❌ Time slot conflicts with existing bookings:', data);
    }

    return isAvailable;
  } catch (error) {
    console.error('Error checking time slot availability:', error);

    // In development, be more lenient with errors
    if (process.env.NODE_ENV === 'development') {
      console.warn('🎯 Development mode: Allowing booking due to error');
      return true;
    }

    // In production, be strict about errors
    return false;
  }
};

// Get real-time availability for a venue/coach on a specific date
export const getTimeSlotAvailability = async ({
  venue_id,
  coach_id,
  booking_date,
}: {
  venue_id?: number;
  coach_id?: number;
  booking_date: string;
}) => {
  try {
    // Get available time slots from the database
    let slotsQuery = supabase
      .from('available_time_slots')
      .select('*')
      .eq('date', booking_date)
      .eq('is_available', true)
      .order('start_time');

    if (venue_id) {
      slotsQuery = slotsQuery.eq('venue_id', venue_id);
    } else if (coach_id) {
      slotsQuery = slotsQuery.eq('coach_id', coach_id);
    }

    const { data: availableSlots, error: slotsError } = await slotsQuery;

    if (slotsError) {
      console.error('Error fetching available slots:', slotsError);
      return [];
    }

    // Get existing bookings for the date to check conflicts
    let bookingsQuery = supabase
      .from('bookings')
      .select('start_time, end_time, status')
      .eq('booking_date', booking_date)
      .in('status', ['confirmed', 'pending']);

    if (venue_id) {
      bookingsQuery = bookingsQuery.eq('venue_id', venue_id);
    } else if (coach_id) {
      bookingsQuery = bookingsQuery.eq('coach_id', coach_id);
    }

    const { data: existingBookings, error: bookingsError } = await bookingsQuery;

    if (bookingsError) {
      console.error('Error fetching existing bookings:', bookingsError);
      return [];
    }

    // Filter out slots that conflict with existing bookings
    const timeSlots = availableSlots?.map(slot => {
      const startTime = slot.start_time.substring(0, 5); // Convert "HH:MM:SS" to "HH:MM"
      const endTime = slot.end_time.substring(0, 5);

      // Check if this slot conflicts with existing bookings
      const isBooked = existingBookings?.some(booking => {
        const bookingStart = booking.start_time.substring(0, 5);
        const bookingEnd = booking.end_time.substring(0, 5);

        return (
          (startTime >= bookingStart && startTime < bookingEnd) ||
          (endTime > bookingStart && endTime <= bookingEnd) ||
          (startTime <= bookingStart && endTime >= bookingEnd)
        );
      });

      return {
        id: slot.id,
        start_time: startTime,
        end_time: endTime,
        is_available: !isBooked,
        price: parseFloat(slot.price),
        currency: slot.currency,
      };
    }).filter(slot => slot.is_available) || [];

    return timeSlots;
  } catch (error) {
    console.error('Error getting time slot availability:', error);
    return [];
  }
};

// Booking Management
export const createBooking = async (bookingData: {
  user_id: string;
  venue_id?: number;
  coach_id?: number;
  booking_date: string;
  start_time: string;
  end_time: string;
  duration_hours: number;
  total_players: number;
  price_per_hour: number;
  total_amount: number;
}) => {
  try {
    console.log('Creating booking with data:', bookingData);

    // First check availability to prevent double booking
    console.log('🔍 About to check time slot availability...');
    const isAvailable = await checkTimeSlotAvailability({
      venue_id: bookingData.venue_id,
      coach_id: bookingData.coach_id,
      booking_date: bookingData.booking_date,
      start_time: bookingData.start_time,
      end_time: bookingData.end_time,
    });

    console.log('✅ Availability check result:', isAvailable);

    if (!isAvailable) {
      console.error('❌ Time slot not available, throwing error');
      throw new Error('Time slot is no longer available');
    }

    console.log('✅ Time slot is available, proceeding with booking creation');

    // Create booking in Supabase
    const { data, error } = await supabase
      .from('bookings')
      .insert({
        user_id: bookingData.user_id,
        venue_id: bookingData.venue_id,
        coach_id: bookingData.coach_id,
        booking_date: bookingData.booking_date,
        start_time: bookingData.start_time,
        end_time: bookingData.end_time,
        duration_hours: bookingData.duration_hours,
        player_count: bookingData.total_players,
        price_per_hour: bookingData.price_per_hour,
        total_amount: bookingData.total_amount,
        currency: 'BDT',
        status: 'pending',
        payment_status: 'pending',
      })
      .select()
      .single();

    if (error) {
      console.error('Supabase booking error:', error);

      // For demo purposes, if table doesn't exist, create a mock booking
      if (error.message.includes('relation') || error.message.includes('table')) {
        console.log('Database table not found, creating mock booking for demo');
        const mockBooking = {
          id: Math.floor(Math.random() * 10000),
          user_id: bookingData.user_id,
          venue_id: bookingData.venue_id,
          coach_id: bookingData.coach_id,
          booking_date: bookingData.booking_date,
          start_time: bookingData.start_time,
          end_time: bookingData.end_time,
          duration_hours: bookingData.duration_hours,
          player_count: bookingData.total_players,
          price_per_hour: bookingData.price_per_hour,
          total_amount: bookingData.total_amount,
          currency: 'BDT',
          status: 'pending',
          payment_status: 'pending',
          created_at: new Date().toISOString(),
        };
        console.log('Mock booking created:', mockBooking);
        return mockBooking;
      }

      throw new Error('Failed to create booking: ' + error.message);
    }

    console.log('Booking created successfully:', data);
    return data;
  } catch (error) {
    console.error('Error creating booking:', error);
    throw error;
  }
};

export const updateBookingStatus = async (
  bookingId: string,
  status: string,
  additionalData?: any
) => {
  try {
    console.log('Updating booking status:', { bookingId, status, additionalData });

    const updateData: any = {
      status,
      updated_at: new Date().toISOString(),
    };

    // Add payment-related data if provided
    if (additionalData) {
      if (additionalData.payment_id) {
        updateData.payment_transaction_id = additionalData.payment_id;
      }
      if (additionalData.payment_method) {
        updateData.payment_method = additionalData.payment_method;
      }
      if (additionalData.paid_amount) {
        updateData.paid_at = new Date().toISOString();
      }
      if (status === 'confirmed') {
        updateData.payment_status = 'paid';
      }
    }

    const { data, error } = await supabase
      .from('bookings')
      .update(updateData)
      .eq('id', parseInt(bookingId))
      .select()
      .single();

    if (error) {
      console.error('Error updating booking status:', error);

      // For demo purposes, if table doesn't exist, create a mock response
      if (error.message.includes('relation') || error.message.includes('table')) {
        console.log('Database table not found, creating mock update response for demo');
        const mockUpdatedBooking = {
          id: parseInt(bookingId),
          status,
          ...updateData,
        };
        console.log('Mock booking update:', mockUpdatedBooking);
        return mockUpdatedBooking;
      }

      throw new Error('Failed to update booking status: ' + error.message);
    }

    console.log('Booking status updated successfully:', data);
    return data;
  } catch (error) {
    console.error('Error updating booking status:', error);
    throw error;
  }
};

// Notification Management
export const createNotification = async (notificationData: {
  user_id: string;
  title: string;
  message: string;
  type: string;
  action_url?: string;
}) => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    // Mock notification creation - replace with actual Supabase call
    const notification = {
      id: Math.floor(Math.random() * 10000),
      ...notificationData,
      is_read: false,
      created_at: new Date().toISOString(),
    };

    console.log('Creating notification:', notification);

    // Add to mock notifications array
    mockNotifications.unshift(notification);

    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
};
