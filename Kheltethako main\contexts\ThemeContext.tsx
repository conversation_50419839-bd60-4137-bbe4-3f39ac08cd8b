import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Appearance, ColorSchemeName } from 'react-native';

export type Theme = 'light' | 'dark' | 'system';

interface ThemeColors {
  background: string;
  surface: string;
  card: string;
  text: string;
  textSecondary: string;
  textTertiary: string;
  primary: string;
  primaryLight: string;
  primaryDark: string;
  secondary: string;
  accent: string;
  border: string;
  borderLight: string;
  success: string;
  warning: string;
  error: string;
  info: string;
  shadow: string;
  overlay: string;
  disabled: string;
  placeholder: string;
}

interface ThemeContextType {
  theme: Theme;
  isDark: boolean;
  colors: ThemeColors;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
}

const lightColors: ThemeColors = {
  background: '#FFFFFF',
  surface: '#F9FAFB',
  card: '#FFFFFF',
  text: '#111827',
  textSecondary: '#374151',
  textTertiary: '#6B7280',
  primary: '#EF4444',
  primaryLight: '#FEF2F2',
  primaryDark: '#DC2626',
  secondary: '#F3F4F6',
  accent: '#3B82F6',
  border: '#E5E7EB',
  borderLight: '#F3F4F6',
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',
  shadow: 'rgba(0, 0, 0, 0.1)',
  overlay: 'rgba(0, 0, 0, 0.5)',
  disabled: '#D1D5DB',
  placeholder: '#9CA3AF',
};

const darkColors: ThemeColors = {
  background: '#111827',
  surface: '#1F2937',
  card: '#374151',
  text: '#F9FAFB',
  textSecondary: '#E5E7EB',
  textTertiary: '#9CA3AF',
  primary: '#EF4444',
  primaryLight: '#7F1D1D',
  primaryDark: '#FCA5A5',
  secondary: '#374151',
  accent: '#60A5FA',
  border: '#4B5563',
  borderLight: '#374151',
  success: '#34D399',
  warning: '#FBBF24',
  error: '#F87171',
  info: '#60A5FA',
  shadow: 'rgba(0, 0, 0, 0.3)',
  overlay: 'rgba(0, 0, 0, 0.7)',
  disabled: '#6B7280',
  placeholder: '#6B7280',
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<Theme>('system');
  const [systemColorScheme, setSystemColorScheme] = useState<ColorSchemeName>(
    Appearance.getColorScheme()
  );

  useEffect(() => {
    // Load saved theme preference
    loadThemePreference();

    // Listen for system theme changes
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      setSystemColorScheme(colorScheme);
    });

    return () => subscription?.remove();
  }, []);

  const loadThemePreference = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem('theme');
      if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
        setThemeState(savedTheme as Theme);
      }
    } catch (error) {
      console.error('Error loading theme preference:', error);
    }
  };

  const saveThemePreference = async (newTheme: Theme) => {
    try {
      await AsyncStorage.setItem('theme', newTheme);
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
  };

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    saveThemePreference(newTheme);
  };

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
  };

  const getEffectiveTheme = (): 'light' | 'dark' => {
    if (theme === 'system') {
      return systemColorScheme === 'dark' ? 'dark' : 'light';
    }
    return theme;
  };

  const isDark = getEffectiveTheme() === 'dark';
  const colors = isDark ? darkColors : lightColors;

  const value: ThemeContextType = {
    theme,
    isDark,
    colors,
    setTheme,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Helper function to create theme-aware styles
export const createThemedStyles = <T extends Record<string, any>>(
  styleCreator: (colors: ThemeColors, isDark: boolean) => T
) => {
  return (colors: ThemeColors, isDark: boolean): T => {
    return styleCreator(colors, isDark);
  };
};

// Common theme-aware style utilities
export const getThemedTextStyle = (colors: ThemeColors, variant: 'primary' | 'secondary' | 'tertiary' = 'primary') => {
  switch (variant) {
    case 'secondary':
      return { color: colors.textSecondary };
    case 'tertiary':
      return { color: colors.textTertiary };
    default:
      return { color: colors.text };
  }
};

export const getThemedBackgroundStyle = (colors: ThemeColors, variant: 'background' | 'surface' | 'card' = 'background') => {
  switch (variant) {
    case 'surface':
      return { backgroundColor: colors.surface };
    case 'card':
      return { backgroundColor: colors.card };
    default:
      return { backgroundColor: colors.background };
  }
};

export const getThemedBorderStyle = (colors: ThemeColors, variant: 'default' | 'light' = 'default') => {
  return {
    borderColor: variant === 'light' ? colors.borderLight : colors.border,
  };
};

export const getThemedShadowStyle = (colors: ThemeColors, elevation: number = 2) => {
  return {
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: elevation },
    shadowOpacity: 1,
    shadowRadius: elevation * 2,
    elevation: elevation,
  };
};

// Theme-aware component props
export interface ThemedComponentProps {
  colors?: ThemeColors;
  isDark?: boolean;
}

// Export theme constants for direct use
export const THEME_CONSTANTS = {
  BORDER_RADIUS: {
    small: 6,
    medium: 8,
    large: 12,
    xlarge: 16,
    round: 50,
  },
  SPACING: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  FONT_SIZES: {
    xs: 10,
    sm: 12,
    md: 14,
    lg: 16,
    xl: 18,
    xxl: 20,
    xxxl: 24,
    xxxxl: 32,
  },
  FONT_WEIGHTS: {
    regular: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
  ANIMATION: {
    duration: {
      fast: 150,
      normal: 250,
      slow: 350,
    },
    easing: {
      ease: 'ease',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      easeInOut: 'ease-in-out',
    },
  },
} as const;
