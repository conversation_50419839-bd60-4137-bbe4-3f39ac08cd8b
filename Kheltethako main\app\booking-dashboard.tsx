import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { 
  ArrowLeft, 
  Calendar, 
  Clock, 
  Users, 
  DollarSign,
  TrendingUp,
  TrendingDown,
  BarChart3,
  Filter,
  Download,
  Eye,
  CheckCircle,
  XCircle,
  AlertCircle,
  Plus
} from 'lucide-react-native';
import { useAuth } from '@/contexts/AuthContext';
import { getBookingDashboardData } from '@/services/supabaseApi';
import { trackPageView } from '@/services/analyticsService';

const { width } = Dimensions.get('window');

interface DashboardStats {
  totalBookings: number;
  totalRevenue: number;
  averageBookingValue: number;
  occupancyRate: number;
  pendingBookings: number;
  confirmedBookings: number;
  cancelledBookings: number;
  completedBookings: number;
  monthlyGrowth: number;
  revenueGrowth: number;
}

interface RecentBooking {
  id: number;
  customerName: string;
  venueName: string;
  date: string;
  time: string;
  amount: number;
  status: string;
  type: 'venue' | 'coach';
}

export default function BookingDashboardScreen() {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentBookings, setRecentBookings] = useState<RecentBooking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month');

  useEffect(() => {
    if (user) {
      trackPageView('/booking-dashboard', user.id);
      fetchDashboardData();
    }
  }, [user, selectedPeriod]);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      if (user?.id) {
        const data = await getBookingDashboardData(user.id, selectedPeriod);
        setStats(data.stats);
        setRecentBookings(data.recentBookings);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const onRefresh = () => {
    setIsRefreshing(true);
    fetchDashboardData();
  };

  const formatCurrency = (amount: number) => {
    return `৳${amount.toLocaleString()}`;
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return '#10B981';
      case 'pending':
        return '#F59E0B';
      case 'cancelled':
        return '#EF4444';
      case 'completed':
        return '#3B82F6';
      default:
        return '#6B7280';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return <CheckCircle size={16} color="#10B981" />;
      case 'pending':
        return <AlertCircle size={16} color="#F59E0B" />;
      case 'cancelled':
        return <XCircle size={16} color="#EF4444" />;
      case 'completed':
        return <CheckCircle size={16} color="#3B82F6" />;
      default:
        return <Clock size={16} color="#6B7280" />;
    }
  };

  const renderStatCard = (
    title: string,
    value: string | number,
    icon: React.ReactNode,
    growth?: number,
    isPercentage?: boolean
  ) => (
    <View style={styles.statCard}>
      <View style={styles.statHeader}>
        <View style={styles.statIcon}>{icon}</View>
        {growth !== undefined && (
          <View style={[
            styles.growthIndicator,
            { backgroundColor: growth >= 0 ? '#DCFCE7' : '#FEE2E2' }
          ]}>
            {growth >= 0 ? (
              <TrendingUp size={12} color="#16A34A" />
            ) : (
              <TrendingDown size={12} color="#DC2626" />
            )}
            <Text style={[
              styles.growthText,
              { color: growth >= 0 ? '#16A34A' : '#DC2626' }
            ]}>
              {Math.abs(growth).toFixed(1)}%
            </Text>
          </View>
        )}
      </View>
      <Text style={styles.statValue}>
        {typeof value === 'number' ? 
          (isPercentage ? `${value.toFixed(1)}%` : value.toLocaleString()) : 
          value
        }
      </Text>
      <Text style={styles.statTitle}>{title}</Text>
    </View>
  );

  const renderRecentBooking = (booking: RecentBooking) => (
    <TouchableOpacity
      key={booking.id}
      style={styles.bookingItem}
      onPress={() => router.push(`/booking-details/${booking.id}`)}
    >
      <View style={styles.bookingHeader}>
        <View style={styles.bookingInfo}>
          <Text style={styles.bookingCustomer}>{booking.customerName}</Text>
          <Text style={styles.bookingVenue}>{booking.venueName}</Text>
        </View>
        <View style={styles.bookingStatus}>
          {getStatusIcon(booking.status)}
          <Text style={[
            styles.bookingStatusText,
            { color: getStatusColor(booking.status) }
          ]}>
            {booking.status}
          </Text>
        </View>
      </View>
      <View style={styles.bookingDetails}>
        <View style={styles.bookingDateTime}>
          <Calendar size={14} color="#6B7280" />
          <Text style={styles.bookingDate}>{booking.date}</Text>
          <Clock size={14} color="#6B7280" />
          <Text style={styles.bookingTime}>{booking.time}</Text>
        </View>
        <Text style={styles.bookingAmount}>{formatCurrency(booking.amount)}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color="#111827" />
        </TouchableOpacity>
        <Text style={styles.title}>Booking Dashboard</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.headerButton}>
            <Filter size={20} color="#6B7280" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerButton}>
            <Download size={20} color="#6B7280" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Period Selector */}
      <View style={styles.periodSelector}>
        {['week', 'month', 'year'].map((period) => (
          <TouchableOpacity
            key={period}
            style={[
              styles.periodButton,
              selectedPeriod === period && styles.periodButtonActive
            ]}
            onPress={() => setSelectedPeriod(period as any)}
          >
            <Text style={[
              styles.periodButtonText,
              selectedPeriod === period && styles.periodButtonTextActive
            ]}>
              {period.charAt(0).toUpperCase() + period.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={['#EF4444']}
            tintColor="#EF4444"
          />
        }
      >
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#EF4444" />
            <Text style={styles.loadingText}>Loading dashboard...</Text>
          </View>
        ) : (
          <>
            {/* Stats Grid */}
            <View style={styles.statsGrid}>
              {renderStatCard(
                'Total Bookings',
                stats?.totalBookings || 0,
                <Calendar size={20} color="#EF4444" />,
                stats?.monthlyGrowth
              )}
              {renderStatCard(
                'Total Revenue',
                formatCurrency(stats?.totalRevenue || 0),
                <DollarSign size={20} color="#10B981" />,
                stats?.revenueGrowth
              )}
              {renderStatCard(
                'Avg. Booking Value',
                formatCurrency(stats?.averageBookingValue || 0),
                <TrendingUp size={20} color="#3B82F6" />
              )}
              {renderStatCard(
                'Occupancy Rate',
                stats?.occupancyRate || 0,
                <BarChart3 size={20} color="#F59E0B" />,
                undefined,
                true
              )}
            </View>

            {/* Status Overview */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Booking Status Overview</Text>
              <View style={styles.statusGrid}>
                <View style={styles.statusCard}>
                  <CheckCircle size={24} color="#10B981" />
                  <Text style={styles.statusValue}>{stats?.confirmedBookings || 0}</Text>
                  <Text style={styles.statusLabel}>Confirmed</Text>
                </View>
                <View style={styles.statusCard}>
                  <AlertCircle size={24} color="#F59E0B" />
                  <Text style={styles.statusValue}>{stats?.pendingBookings || 0}</Text>
                  <Text style={styles.statusLabel}>Pending</Text>
                </View>
                <View style={styles.statusCard}>
                  <CheckCircle size={24} color="#3B82F6" />
                  <Text style={styles.statusValue}>{stats?.completedBookings || 0}</Text>
                  <Text style={styles.statusLabel}>Completed</Text>
                </View>
                <View style={styles.statusCard}>
                  <XCircle size={24} color="#EF4444" />
                  <Text style={styles.statusValue}>{stats?.cancelledBookings || 0}</Text>
                  <Text style={styles.statusLabel}>Cancelled</Text>
                </View>
              </View>
            </View>

            {/* Recent Bookings */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Recent Bookings</Text>
                <TouchableOpacity
                  style={styles.viewAllButton}
                  onPress={() => router.push('/all-bookings')}
                >
                  <Eye size={16} color="#EF4444" />
                  <Text style={styles.viewAllText}>View All</Text>
                </TouchableOpacity>
              </View>
              <View style={styles.bookingsList}>
                {recentBookings.length > 0 ? (
                  recentBookings.map(renderRecentBooking)
                ) : (
                  <View style={styles.emptyState}>
                    <Calendar size={48} color="#D1D5DB" />
                    <Text style={styles.emptyStateTitle}>No Recent Bookings</Text>
                    <Text style={styles.emptyStateText}>
                      Your recent bookings will appear here
                    </Text>
                  </View>
                )}
              </View>
            </View>

            {/* Quick Actions */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Quick Actions</Text>
              <View style={styles.quickActions}>
                <TouchableOpacity
                  style={styles.quickActionButton}
                  onPress={() => router.push('/create-booking')}
                >
                  <Plus size={20} color="#FFFFFF" />
                  <Text style={styles.quickActionText}>New Booking</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.quickActionButton, styles.quickActionButtonSecondary]}
                  onPress={() => router.push('/analytics')}
                >
                  <BarChart3 size={20} color="#EF4444" />
                  <Text style={[styles.quickActionText, styles.quickActionTextSecondary]}>
                    View Analytics
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.bottomSpacing} />
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    fontFamily: 'Inter-SemiBold',
    flex: 1,
    textAlign: 'center',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerButton: {
    padding: 8,
  },
  periodSelector: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#F9FAFB',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  periodButtonActive: {
    backgroundColor: '#EF4444',
  },
  periodButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  periodButtonTextActive: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 12,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginTop: 16,
  },
  statCard: {
    width: (width - 44) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FEF2F2',
    justifyContent: 'center',
    alignItems: 'center',
  },
  growthIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 12,
    gap: 2,
  },
  growthText: {
    fontSize: 10,
    fontFamily: 'Inter-SemiBold',
  },
  statValue: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  statTitle: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  section: {
    marginTop: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  viewAllText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#EF4444',
  },
  statusGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statusCard: {
    width: (width - 56) / 4,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  statusValue: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginTop: 8,
    marginBottom: 4,
  },
  statusLabel: {
    fontSize: 10,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    textAlign: 'center',
  },
  bookingsList: {
    gap: 12,
  },
  bookingItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  bookingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  bookingInfo: {
    flex: 1,
  },
  bookingCustomer: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 2,
  },
  bookingVenue: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  bookingStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  bookingStatusText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    textTransform: 'capitalize',
  },
  bookingDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  bookingDateTime: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  bookingDate: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  bookingTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  bookingAmount: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#EF4444',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 48,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
  quickActions: {
    flexDirection: 'row',
    gap: 12,
  },
  quickActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#EF4444',
    borderRadius: 12,
    paddingVertical: 16,
    gap: 8,
  },
  quickActionButtonSecondary: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#EF4444',
  },
  quickActionText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  quickActionTextSecondary: {
    color: '#EF4444',
  },
  bottomSpacing: {
    height: 32,
  },
});
