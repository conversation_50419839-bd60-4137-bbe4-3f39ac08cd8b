/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/booking-confirmation`; params?: Router.UnknownInputParams; } | { pathname: `/booking-history`; params?: Router.UnknownInputParams; } | { pathname: `/coach-booking-confirmation`; params?: Router.UnknownInputParams; } | { pathname: `/edit-profile`; params?: Router.UnknownInputParams; } | { pathname: `/favorites`; params?: Router.UnknownInputParams; } | { pathname: `/help`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `/payment-cancel`; params?: Router.UnknownInputParams; } | { pathname: `/payment-methods`; params?: Router.UnknownInputParams; } | { pathname: `/payment-success`; params?: Router.UnknownInputParams; } | { pathname: `/payment`; params?: Router.UnknownInputParams; } | { pathname: `/personal-info`; params?: Router.UnknownInputParams; } | { pathname: `/privacy-settings`; params?: Router.UnknownInputParams; } | { pathname: `/review-modal`; params?: Router.UnknownInputParams; } | { pathname: `/saved-locations`; params?: Router.UnknownInputParams; } | { pathname: `/settings`; params?: Router.UnknownInputParams; } | { pathname: `/../supabase/functions/analytics/index`; params?: Router.UnknownInputParams; } | { pathname: `/filters`; params?: Router.UnknownInputParams; } | { pathname: `/search-results`; params?: Router.UnknownInputParams; } | { pathname: `/cancel-booking`; params?: Router.UnknownInputParams; } | { pathname: `/chat`; params?: Router.UnknownInputParams; } | { pathname: `/chat/[id]`; params?: Router.UnknownInputParams; } | { pathname: `/booking-dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/../contexts/ThemeContext`; params?: Router.UnknownInputParams; } | { pathname: `/theme-settings`; params?: Router.UnknownInputParams; } | { pathname: `/../contexts/LanguageContext`; params?: Router.UnknownInputParams; } | { pathname: `/language-settings`; params?: Router.UnknownInputParams; } | { pathname: `/map-search`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/forgot-password` | `/forgot-password`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/register` | `/register`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bookings` | `/bookings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/admin/analytics`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/booking/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/booking-details/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/coach/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/venue/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/booking-confirmation`; params?: Router.UnknownOutputParams; } | { pathname: `/booking-history`; params?: Router.UnknownOutputParams; } | { pathname: `/coach-booking-confirmation`; params?: Router.UnknownOutputParams; } | { pathname: `/edit-profile`; params?: Router.UnknownOutputParams; } | { pathname: `/favorites`; params?: Router.UnknownOutputParams; } | { pathname: `/help`; params?: Router.UnknownOutputParams; } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/notifications`; params?: Router.UnknownOutputParams; } | { pathname: `/payment-cancel`; params?: Router.UnknownOutputParams; } | { pathname: `/payment-methods`; params?: Router.UnknownOutputParams; } | { pathname: `/payment-success`; params?: Router.UnknownOutputParams; } | { pathname: `/payment`; params?: Router.UnknownOutputParams; } | { pathname: `/personal-info`; params?: Router.UnknownOutputParams; } | { pathname: `/privacy-settings`; params?: Router.UnknownOutputParams; } | { pathname: `/review-modal`; params?: Router.UnknownOutputParams; } | { pathname: `/saved-locations`; params?: Router.UnknownOutputParams; } | { pathname: `/settings`; params?: Router.UnknownOutputParams; } | { pathname: `/../supabase/functions/analytics/index`; params?: Router.UnknownOutputParams; } | { pathname: `/filters`; params?: Router.UnknownOutputParams; } | { pathname: `/search-results`; params?: Router.UnknownOutputParams; } | { pathname: `/cancel-booking`; params?: Router.UnknownOutputParams; } | { pathname: `/chat`; params?: Router.UnknownOutputParams; } | { pathname: `/chat/[id]`; params?: Router.UnknownOutputParams; } | { pathname: `/booking-dashboard`; params?: Router.UnknownOutputParams; } | { pathname: `/../contexts/ThemeContext`; params?: Router.UnknownOutputParams; } | { pathname: `/theme-settings`; params?: Router.UnknownOutputParams; } | { pathname: `/../contexts/LanguageContext`; params?: Router.UnknownOutputParams; } | { pathname: `/language-settings`; params?: Router.UnknownOutputParams; } | { pathname: `/map-search`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/forgot-password` | `/forgot-password`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/register` | `/register`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/bookings` | `/bookings`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `/admin/analytics`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } } | { pathname: `/booking/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/booking-details/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/coach/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/venue/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/booking-confirmation${`?${string}` | `#${string}` | ''}` | `/booking-history${`?${string}` | `#${string}` | ''}` | `/coach-booking-confirmation${`?${string}` | `#${string}` | ''}` | `/edit-profile${`?${string}` | `#${string}` | ''}` | `/favorites${`?${string}` | `#${string}` | ''}` | `/help${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `/notifications${`?${string}` | `#${string}` | ''}` | `/payment-cancel${`?${string}` | `#${string}` | ''}` | `/payment-methods${`?${string}` | `#${string}` | ''}` | `/payment-success${`?${string}` | `#${string}` | ''}` | `/payment${`?${string}` | `#${string}` | ''}` | `/personal-info${`?${string}` | `#${string}` | ''}` | `/privacy-settings${`?${string}` | `#${string}` | ''}` | `/review-modal${`?${string}` | `#${string}` | ''}` | `/saved-locations${`?${string}` | `#${string}` | ''}` | `/settings${`?${string}` | `#${string}` | ''}` | `/../supabase/functions/analytics/index${`?${string}` | `#${string}` | ''}` | `/filters${`?${string}` | `#${string}` | ''}` | `/search-results${`?${string}` | `#${string}` | ''}` | `/cancel-booking${`?${string}` | `#${string}` | ''}` | `/chat${`?${string}` | `#${string}` | ''}` | `/chat/[id]${`?${string}` | `#${string}` | ''}` | `/booking-dashboard${`?${string}` | `#${string}` | ''}` | `/../contexts/ThemeContext${`?${string}` | `#${string}` | ''}` | `/theme-settings${`?${string}` | `#${string}` | ''}` | `/../contexts/LanguageContext${`?${string}` | `#${string}` | ''}` | `/language-settings${`?${string}` | `#${string}` | ''}` | `/map-search${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/forgot-password${`?${string}` | `#${string}` | ''}` | `/forgot-password${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/login${`?${string}` | `#${string}` | ''}` | `/login${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/register${`?${string}` | `#${string}` | ''}` | `/register${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/bookings${`?${string}` | `#${string}` | ''}` | `/bookings${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/explore${`?${string}` | `#${string}` | ''}` | `/explore${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/profile${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `/admin/analytics${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/booking-confirmation`; params?: Router.UnknownInputParams; } | { pathname: `/booking-history`; params?: Router.UnknownInputParams; } | { pathname: `/coach-booking-confirmation`; params?: Router.UnknownInputParams; } | { pathname: `/edit-profile`; params?: Router.UnknownInputParams; } | { pathname: `/favorites`; params?: Router.UnknownInputParams; } | { pathname: `/help`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `/payment-cancel`; params?: Router.UnknownInputParams; } | { pathname: `/payment-methods`; params?: Router.UnknownInputParams; } | { pathname: `/payment-success`; params?: Router.UnknownInputParams; } | { pathname: `/payment`; params?: Router.UnknownInputParams; } | { pathname: `/personal-info`; params?: Router.UnknownInputParams; } | { pathname: `/privacy-settings`; params?: Router.UnknownInputParams; } | { pathname: `/review-modal`; params?: Router.UnknownInputParams; } | { pathname: `/saved-locations`; params?: Router.UnknownInputParams; } | { pathname: `/settings`; params?: Router.UnknownInputParams; } | { pathname: `/../supabase/functions/analytics/index`; params?: Router.UnknownInputParams; } | { pathname: `/filters`; params?: Router.UnknownInputParams; } | { pathname: `/search-results`; params?: Router.UnknownInputParams; } | { pathname: `/cancel-booking`; params?: Router.UnknownInputParams; } | { pathname: `/chat`; params?: Router.UnknownInputParams; } | { pathname: `/chat/[id]`; params?: Router.UnknownInputParams; } | { pathname: `/booking-dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/../contexts/ThemeContext`; params?: Router.UnknownInputParams; } | { pathname: `/theme-settings`; params?: Router.UnknownInputParams; } | { pathname: `/../contexts/LanguageContext`; params?: Router.UnknownInputParams; } | { pathname: `/language-settings`; params?: Router.UnknownInputParams; } | { pathname: `/map-search`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/forgot-password` | `/forgot-password`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/register` | `/register`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bookings` | `/bookings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/admin/analytics`; params?: Router.UnknownInputParams; } | `/+not-found` | `/booking/${Router.SingleRoutePart<T>}` | `/booking-details/${Router.SingleRoutePart<T>}` | `/coach/${Router.SingleRoutePart<T>}` | `/venue/${Router.SingleRoutePart<T>}` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/booking/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/booking-details/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/coach/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/venue/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
