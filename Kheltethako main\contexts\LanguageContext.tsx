import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Localization from 'expo-localization';

export type SupportedLanguage = 'en' | 'bn' | 'hi' | 'ur';

interface LanguageContextType {
  language: SupportedLanguage;
  setLanguage: (language: SupportedLanguage) => void;
  t: (key: string, params?: Record<string, string | number>) => string;
  isRTL: boolean;
}

// Translation keys and values
const translations = {
  en: {
    // Common
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.cancel': 'Cancel',
    'common.confirm': 'Confirm',
    'common.save': 'Save',
    'common.edit': 'Edit',
    'common.delete': 'Delete',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'common.sort': 'Sort',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.done': 'Done',
    'common.close': 'Close',
    'common.view_all': 'View All',
    'common.see_more': 'See More',
    'common.try_again': 'Try Again',
    
    // Navigation
    'nav.home': 'Home',
    'nav.explore': 'Explore',
    'nav.bookings': 'Bookings',
    'nav.profile': 'Profile',
    'nav.chat': 'Messages',
    
    // Auth
    'auth.login': 'Login',
    'auth.register': 'Register',
    'auth.logout': 'Logout',
    'auth.email': 'Email',
    'auth.password': 'Password',
    'auth.confirm_password': 'Confirm Password',
    'auth.full_name': 'Full Name',
    'auth.phone': 'Phone Number',
    'auth.forgot_password': 'Forgot Password?',
    'auth.remember_me': 'Remember Me',
    'auth.login_success': 'Login successful',
    'auth.register_success': 'Registration successful',
    'auth.invalid_credentials': 'Invalid email or password',
    
    // Home
    'home.welcome': 'Welcome to KhelteThako',
    'home.find_venues': 'Find Sports Venues',
    'home.book_coaches': 'Book Professional Coaches',
    'home.popular_sports': 'Popular Sports',
    'home.featured_venues': 'Featured Venues',
    'home.recommended_coaches': 'Recommended Coaches',
    'home.quick_book': 'Quick Book',
    'home.search_placeholder': 'Search venues, sports, locations...',
    
    // Explore
    'explore.title': 'Explore',
    'explore.venues': 'Venues',
    'explore.coaches': 'Coaches',
    'explore.sports': 'Sports',
    'explore.near_you': 'Near You',
    'explore.top_rated': 'Top Rated',
    'explore.available_now': 'Available Now',
    
    // Bookings
    'bookings.title': 'My Bookings',
    'bookings.upcoming': 'Upcoming',
    'bookings.past': 'Past',
    'bookings.cancelled': 'Cancelled',
    'bookings.no_bookings': 'No bookings found',
    'bookings.book_now': 'Book Now',
    'bookings.cancel_booking': 'Cancel Booking',
    'bookings.reschedule': 'Reschedule',
    'bookings.view_details': 'View Details',
    
    // Profile
    'profile.title': 'Profile',
    'profile.personal_info': 'Personal Information',
    'profile.settings': 'Settings',
    'profile.privacy': 'Privacy',
    'profile.help': 'Help & Support',
    'profile.about': 'About',
    'profile.logout': 'Logout',
    'profile.edit_profile': 'Edit Profile',
    'profile.saved_locations': 'Saved Locations',
    'profile.payment_methods': 'Payment Methods',
    'profile.notifications': 'Notifications',
    'profile.language': 'Language',
    'profile.theme': 'Theme',
    
    // Booking Flow
    'booking.select_date': 'Select Date',
    'booking.select_time': 'Select Time',
    'booking.duration': 'Duration',
    'booking.total_amount': 'Total Amount',
    'booking.payment_method': 'Payment Method',
    'booking.confirm_booking': 'Confirm Booking',
    'booking.booking_confirmed': 'Booking Confirmed',
    'booking.booking_failed': 'Booking Failed',
    'booking.available': 'Available',
    'booking.unavailable': 'Unavailable',
    'booking.per_hour': 'per hour',
    
    // Chat
    'chat.title': 'Messages',
    'chat.no_conversations': 'No conversations',
    'chat.start_chat': 'Start New Chat',
    'chat.type_message': 'Type a message...',
    'chat.online': 'Online',
    'chat.offline': 'Offline',
    'chat.last_seen': 'Last seen',
    
    // Settings
    'settings.title': 'Settings',
    'settings.account': 'Account Settings',
    'settings.privacy': 'Privacy Settings',
    'settings.notifications': 'Notification Settings',
    'settings.language': 'Language Settings',
    'settings.theme': 'Theme Settings',
    'settings.help': 'Help & Support',
    'settings.about': 'About App',
    'settings.version': 'Version',
    
    // Errors
    'error.network': 'Network error. Please check your connection.',
    'error.server': 'Server error. Please try again later.',
    'error.unknown': 'An unexpected error occurred.',
    'error.validation': 'Please check your input and try again.',
    'error.permission': 'Permission denied.',
    'error.not_found': 'Resource not found.',
    
    // Success Messages
    'success.booking_created': 'Booking created successfully',
    'success.booking_cancelled': 'Booking cancelled successfully',
    'success.profile_updated': 'Profile updated successfully',
    'success.payment_completed': 'Payment completed successfully',
    'success.review_submitted': 'Review submitted successfully',
  },
  
  bn: {
    // Common
    'common.loading': 'লোড হচ্ছে...',
    'common.error': 'ত্রুটি',
    'common.success': 'সফল',
    'common.cancel': 'বাতিল',
    'common.confirm': 'নিশ্চিত করুন',
    'common.save': 'সংরক্ষণ',
    'common.edit': 'সম্পাদনা',
    'common.delete': 'মুছুন',
    'common.search': 'অনুসন্ধান',
    'common.filter': 'ফিল্টার',
    'common.sort': 'সাজান',
    'common.back': 'পিছনে',
    'common.next': 'পরবর্তী',
    'common.previous': 'পূর্ববর্তী',
    'common.done': 'সম্পন্ন',
    'common.close': 'বন্ধ',
    'common.view_all': 'সব দেখুন',
    'common.see_more': 'আরো দেখুন',
    'common.try_again': 'আবার চেষ্টা করুন',
    
    // Navigation
    'nav.home': 'হোম',
    'nav.explore': 'অন্বেষণ',
    'nav.bookings': 'বুকিং',
    'nav.profile': 'প্রোফাইল',
    'nav.chat': 'বার্তা',
    
    // Auth
    'auth.login': 'লগইন',
    'auth.register': 'নিবন্ধন',
    'auth.logout': 'লগআউট',
    'auth.email': 'ইমেইল',
    'auth.password': 'পাসওয়ার্ড',
    'auth.confirm_password': 'পাসওয়ার্ড নিশ্চিত করুন',
    'auth.full_name': 'পূর্ণ নাম',
    'auth.phone': 'ফোন নম্বর',
    'auth.forgot_password': 'পাসওয়ার্ড ভুলে গেছেন?',
    'auth.remember_me': 'আমাকে মনে রাখুন',
    'auth.login_success': 'লগইন সফল',
    'auth.register_success': 'নিবন্ধন সফল',
    'auth.invalid_credentials': 'ভুল ইমেইল বা পাসওয়ার্ড',
    
    // Home
    'home.welcome': 'খেলতে থাকো তে স্বাগতম',
    'home.find_venues': 'খেলার মাঠ খুঁজুন',
    'home.book_coaches': 'পেশাদার কোচ বুক করুন',
    'home.popular_sports': 'জনপ্রিয় খেলা',
    'home.featured_venues': 'বিশেষ মাঠ',
    'home.recommended_coaches': 'প্রস্তাবিত কোচ',
    'home.quick_book': 'দ্রুত বুক',
    'home.search_placeholder': 'মাঠ, খেলা, স্থান অনুসন্ধান করুন...',
    
    // Continue with other translations...
    'explore.title': 'অন্বেষণ',
    'bookings.title': 'আমার বুকিং',
    'profile.title': 'প্রোফাইল',
    'chat.title': 'বার্তা',
    'settings.title': 'সেটিংস',
  },
  
  hi: {
    // Common
    'common.loading': 'लोड हो रहा है...',
    'common.error': 'त्रुटि',
    'common.success': 'सफल',
    'common.cancel': 'रद्द करें',
    'common.confirm': 'पुष्टि करें',
    'common.save': 'सेव करें',
    'common.edit': 'संपादित करें',
    'common.delete': 'हटाएं',
    'common.search': 'खोजें',
    'common.filter': 'फिल्टर',
    'common.sort': 'क्रमबद्ध करें',
    'common.back': 'वापस',
    'common.next': 'अगला',
    'common.previous': 'पिछला',
    'common.done': 'पूर्ण',
    'common.close': 'बंद करें',
    'common.view_all': 'सभी देखें',
    'common.see_more': 'और देखें',
    'common.try_again': 'फिर कोशिश करें',
    
    // Navigation
    'nav.home': 'होम',
    'nav.explore': 'एक्सप्लोर',
    'nav.bookings': 'बुकिंग',
    'nav.profile': 'प्रोफाइल',
    'nav.chat': 'संदेश',
    
    // Auth
    'auth.login': 'लॉगिन',
    'auth.register': 'रजिस्टर',
    'auth.logout': 'लॉगआउट',
    'auth.email': 'ईमेल',
    'auth.password': 'पासवर्ड',
    'auth.full_name': 'पूरा नाम',
    'auth.phone': 'फोन नंबर',
    
    // Continue with other translations...
    'home.welcome': 'खेलते थको में आपका स्वागत है',
    'explore.title': 'एक्सप्लोर',
    'bookings.title': 'मेरी बुकिंग',
    'profile.title': 'प्रोफाइल',
    'chat.title': 'संदेश',
    'settings.title': 'सेटिंग्स',
  },
  
  ur: {
    // Common
    'common.loading': 'لوڈ ہو رہا ہے...',
    'common.error': 'خرابی',
    'common.success': 'کامیاب',
    'common.cancel': 'منسوخ',
    'common.confirm': 'تصدیق',
    'common.save': 'محفوظ کریں',
    'common.edit': 'ترمیم',
    'common.delete': 'حذف کریں',
    'common.search': 'تلاش',
    'common.filter': 'فلٹر',
    'common.sort': 'ترتیب',
    'common.back': 'واپس',
    'common.next': 'اگلا',
    'common.previous': 'پچھلا',
    'common.done': 'مکمل',
    'common.close': 'بند کریں',
    'common.view_all': 'سب دیکھیں',
    'common.see_more': 'مزید دیکھیں',
    'common.try_again': 'دوبارہ کوشش کریں',
    
    // Navigation
    'nav.home': 'ہوم',
    'nav.explore': 'دریافت',
    'nav.bookings': 'بکنگ',
    'nav.profile': 'پروفائل',
    'nav.chat': 'پیغامات',
    
    // Auth
    'auth.login': 'لاگ ان',
    'auth.register': 'رجسٹر',
    'auth.logout': 'لاگ آؤٹ',
    'auth.email': 'ای میل',
    'auth.password': 'پاس ورڈ',
    'auth.full_name': 'پورا نام',
    'auth.phone': 'فون نمبر',
    
    // Continue with other translations...
    'home.welcome': 'کھیلتے تھکو میں خوش آمدید',
    'explore.title': 'دریافت',
    'bookings.title': 'میری بکنگ',
    'profile.title': 'پروفائل',
    'chat.title': 'پیغامات',
    'settings.title': 'ترتیبات',
  },
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, setLanguageState] = useState<SupportedLanguage>('en');

  useEffect(() => {
    loadLanguagePreference();
  }, []);

  const loadLanguagePreference = async () => {
    try {
      const savedLanguage = await AsyncStorage.getItem('language');
      if (savedLanguage && ['en', 'bn', 'hi', 'ur'].includes(savedLanguage)) {
        setLanguageState(savedLanguage as SupportedLanguage);
      } else {
        // Detect system language
        const systemLanguage = Localization.locale.split('-')[0];
        if (['bn', 'hi', 'ur'].includes(systemLanguage)) {
          setLanguageState(systemLanguage as SupportedLanguage);
        }
      }
    } catch (error) {
      console.error('Error loading language preference:', error);
    }
  };

  const saveLanguagePreference = async (newLanguage: SupportedLanguage) => {
    try {
      await AsyncStorage.setItem('language', newLanguage);
    } catch (error) {
      console.error('Error saving language preference:', error);
    }
  };

  const setLanguage = (newLanguage: SupportedLanguage) => {
    setLanguageState(newLanguage);
    saveLanguagePreference(newLanguage);
  };

  const t = (key: string, params?: Record<string, string | number>): string => {
    let translation = translations[language]?.[key] || translations.en[key] || key;
    
    // Replace parameters in translation
    if (params) {
      Object.entries(params).forEach(([paramKey, paramValue]) => {
        translation = translation.replace(`{{${paramKey}}}`, String(paramValue));
      });
    }
    
    return translation;
  };

  const isRTL = language === 'ur';

  const value: LanguageContextType = {
    language,
    setLanguage,
    t,
    isRTL,
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

// Language options for settings
export const LANGUAGE_OPTIONS = [
  {
    code: 'en' as SupportedLanguage,
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
  },
  {
    code: 'bn' as SupportedLanguage,
    name: 'Bengali',
    nativeName: 'বাংলা',
    flag: '🇧🇩',
  },
  {
    code: 'hi' as SupportedLanguage,
    name: 'Hindi',
    nativeName: 'हिन्दी',
    flag: '🇮🇳',
  },
  {
    code: 'ur' as SupportedLanguage,
    name: 'Urdu',
    nativeName: 'اردو',
    flag: '🇵🇰',
  },
];
