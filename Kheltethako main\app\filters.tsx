import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Switch,
  Dimensions,
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { 
  ArrowLeft, 
  Search, 
  MapPin, 
  DollarSign, 
  Star, 
  Calendar,
  Clock,
  Filter,
  X,
  Check
} from 'lucide-react-native';
import { getSports } from '@/services/supabaseApi';
import { trackPageView } from '@/services/analyticsService';

const { width } = Dimensions.get('window');

interface FilterState {
  searchQuery: string;
  selectedSports: string[];
  location: string;
  priceRange: [number, number];
  minRating: number;
  availability: 'any' | 'today' | 'tomorrow' | 'this_week';
  amenities: string[];
  sortBy: 'relevance' | 'price_low' | 'price_high' | 'rating' | 'distance';
  venueType: 'all' | 'indoor' | 'outdoor';
  timeSlot: 'any' | 'morning' | 'afternoon' | 'evening';
}

const AMENITIES_OPTIONS = [
  'Parking', 'Changing Room', 'Shower', 'Equipment Rental',
  'Cafeteria', 'First Aid', 'Air Conditioning', 'WiFi',
  'CCTV', 'Lighting', 'Sound System', 'Scoreboard'
];

const AVAILABILITY_OPTIONS = [
  { value: 'any', label: 'Any Time' },
  { value: 'today', label: 'Today' },
  { value: 'tomorrow', label: 'Tomorrow' },
  { value: 'this_week', label: 'This Week' },
];

const SORT_OPTIONS = [
  { value: 'relevance', label: 'Most Relevant' },
  { value: 'price_low', label: 'Price: Low to High' },
  { value: 'price_high', label: 'Price: High to Low' },
  { value: 'rating', label: 'Highest Rated' },
  { value: 'distance', label: 'Nearest First' },
];

const TIME_SLOT_OPTIONS = [
  { value: 'any', label: 'Any Time' },
  { value: 'morning', label: 'Morning (6AM - 12PM)' },
  { value: 'afternoon', label: 'Afternoon (12PM - 6PM)' },
  { value: 'evening', label: 'Evening (6PM - 12AM)' },
];

export default function FiltersScreen() {
  const [filters, setFilters] = useState<FilterState>({
    searchQuery: '',
    selectedSports: [],
    location: '',
    priceRange: [0, 10000],
    minRating: 0,
    availability: 'any',
    amenities: [],
    sortBy: 'relevance',
    venueType: 'all',
    timeSlot: 'any',
  });

  const [sports, setSports] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    trackPageView('/filters');
    fetchSports();
  }, []);

  const fetchSports = async () => {
    try {
      const sportsData = await getSports();
      setSports(sportsData);
    } catch (error) {
      console.error('Error fetching sports:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSportToggle = (sportId: string) => {
    setFilters(prev => ({
      ...prev,
      selectedSports: prev.selectedSports.includes(sportId)
        ? prev.selectedSports.filter(id => id !== sportId)
        : [...prev.selectedSports, sportId]
    }));
  };

  const handleAmenityToggle = (amenity: string) => {
    setFilters(prev => ({
      ...prev,
      amenities: prev.amenities.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...prev.amenities, amenity]
    }));
  };

  const handlePriceRangeChange = (value: number, isMin: boolean) => {
    setFilters(prev => ({
      ...prev,
      priceRange: isMin 
        ? [value, prev.priceRange[1]]
        : [prev.priceRange[0], value]
    }));
  };

  const clearAllFilters = () => {
    setFilters({
      searchQuery: '',
      selectedSports: [],
      location: '',
      priceRange: [0, 10000],
      minRating: 0,
      availability: 'any',
      amenities: [],
      sortBy: 'relevance',
      venueType: 'all',
      timeSlot: 'any',
    });
  };

  const applyFilters = () => {
    // Navigate back with filters applied
    router.back();
    // In a real implementation, you would pass these filters to the explore screen
    console.log('Applied filters:', filters);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.searchQuery) count++;
    if (filters.selectedSports.length > 0) count++;
    if (filters.location) count++;
    if (filters.priceRange[0] > 0 || filters.priceRange[1] < 10000) count++;
    if (filters.minRating > 0) count++;
    if (filters.availability !== 'any') count++;
    if (filters.amenities.length > 0) count++;
    if (filters.sortBy !== 'relevance') count++;
    if (filters.venueType !== 'all') count++;
    if (filters.timeSlot !== 'any') count++;
    return count;
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color="#111827" />
        </TouchableOpacity>
        <Text style={styles.title}>Filters</Text>
        <TouchableOpacity
          style={styles.clearButton}
          onPress={clearAllFilters}
        >
          <Text style={styles.clearButtonText}>Clear All</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Search Query */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Search</Text>
          <View style={styles.searchContainer}>
            <Search size={20} color="#6B7280" />
            <TextInput
              style={styles.searchInput}
              placeholder="Search venues, sports, locations..."
              placeholderTextColor="#9CA3AF"
              value={filters.searchQuery}
              onChangeText={(text) => setFilters(prev => ({ ...prev, searchQuery: text }))}
            />
          </View>
        </View>

        {/* Sports Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Sports</Text>
          <View style={styles.sportsGrid}>
            {sports.map((sport) => (
              <TouchableOpacity
                key={sport.id}
                style={[
                  styles.sportChip,
                  filters.selectedSports.includes(sport.id.toString()) && styles.sportChipSelected
                ]}
                onPress={() => handleSportToggle(sport.id.toString())}
              >
                <Text style={styles.sportIcon}>{sport.icon || '🏃‍♂️'}</Text>
                <Text style={[
                  styles.sportName,
                  filters.selectedSports.includes(sport.id.toString()) && styles.sportNameSelected
                ]}>
                  {sport.name}
                </Text>
                {filters.selectedSports.includes(sport.id.toString()) && (
                  <Check size={16} color="#FFFFFF" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Location */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Location</Text>
          <View style={styles.locationContainer}>
            <MapPin size={20} color="#6B7280" />
            <TextInput
              style={styles.locationInput}
              placeholder="Enter area, city or venue name"
              placeholderTextColor="#9CA3AF"
              value={filters.location}
              onChangeText={(text) => setFilters(prev => ({ ...prev, location: text }))}
            />
          </View>
        </View>

        {/* Price Range */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Price Range (per hour)</Text>
          <View style={styles.priceRangeContainer}>
            <View style={styles.priceInputContainer}>
              <DollarSign size={16} color="#6B7280" />
              <TextInput
                style={styles.priceInput}
                placeholder="Min"
                placeholderTextColor="#9CA3AF"
                value={filters.priceRange[0].toString()}
                onChangeText={(text) => handlePriceRangeChange(parseInt(text) || 0, true)}
                keyboardType="numeric"
              />
            </View>
            <Text style={styles.priceSeparator}>to</Text>
            <View style={styles.priceInputContainer}>
              <DollarSign size={16} color="#6B7280" />
              <TextInput
                style={styles.priceInput}
                placeholder="Max"
                placeholderTextColor="#9CA3AF"
                value={filters.priceRange[1].toString()}
                onChangeText={(text) => handlePriceRangeChange(parseInt(text) || 10000, false)}
                keyboardType="numeric"
              />
            </View>
          </View>
          <Text style={styles.priceRangeText}>
            ৳{filters.priceRange[0]} - ৳{filters.priceRange[1]}
          </Text>
        </View>

        {/* Rating */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Minimum Rating</Text>
          <View style={styles.ratingContainer}>
            {[1, 2, 3, 4, 5].map((rating) => (
              <TouchableOpacity
                key={rating}
                style={[
                  styles.ratingButton,
                  filters.minRating >= rating && styles.ratingButtonSelected
                ]}
                onPress={() => setFilters(prev => ({ 
                  ...prev, 
                  minRating: prev.minRating === rating ? 0 : rating 
                }))}
              >
                <Star 
                  size={20} 
                  color={filters.minRating >= rating ? "#F59E0B" : "#D1D5DB"}
                  fill={filters.minRating >= rating ? "#F59E0B" : "transparent"}
                />
              </TouchableOpacity>
            ))}
            <Text style={styles.ratingText}>
              {filters.minRating > 0 ? `${filters.minRating}+ stars` : 'Any rating'}
            </Text>
          </View>
        </View>

        {/* Availability */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Availability</Text>
          <View style={styles.optionsContainer}>
            {AVAILABILITY_OPTIONS.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.optionButton,
                  filters.availability === option.value && styles.optionButtonSelected
                ]}
                onPress={() => setFilters(prev => ({ ...prev, availability: option.value as any }))}
              >
                <Text style={[
                  styles.optionText,
                  filters.availability === option.value && styles.optionTextSelected
                ]}>
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Time Slot */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preferred Time</Text>
          <View style={styles.optionsContainer}>
            {TIME_SLOT_OPTIONS.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.optionButton,
                  filters.timeSlot === option.value && styles.optionButtonSelected
                ]}
                onPress={() => setFilters(prev => ({ ...prev, timeSlot: option.value as any }))}
              >
                <Text style={[
                  styles.optionText,
                  filters.timeSlot === option.value && styles.optionTextSelected
                ]}>
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Venue Type */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Venue Type</Text>
          <View style={styles.optionsContainer}>
            {[
              { value: 'all', label: 'All Venues' },
              { value: 'indoor', label: 'Indoor Only' },
              { value: 'outdoor', label: 'Outdoor Only' },
            ].map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.optionButton,
                  filters.venueType === option.value && styles.optionButtonSelected
                ]}
                onPress={() => setFilters(prev => ({ ...prev, venueType: option.value as any }))}
              >
                <Text style={[
                  styles.optionText,
                  filters.venueType === option.value && styles.optionTextSelected
                ]}>
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Amenities */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Amenities</Text>
          <View style={styles.amenitiesGrid}>
            {AMENITIES_OPTIONS.map((amenity) => (
              <TouchableOpacity
                key={amenity}
                style={[
                  styles.amenityChip,
                  filters.amenities.includes(amenity) && styles.amenityChipSelected
                ]}
                onPress={() => handleAmenityToggle(amenity)}
              >
                <Text style={[
                  styles.amenityText,
                  filters.amenities.includes(amenity) && styles.amenityTextSelected
                ]}>
                  {amenity}
                </Text>
                {filters.amenities.includes(amenity) && (
                  <Check size={14} color="#FFFFFF" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Sort By */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Sort By</Text>
          <View style={styles.optionsContainer}>
            {SORT_OPTIONS.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.optionButton,
                  filters.sortBy === option.value && styles.optionButtonSelected
                ]}
                onPress={() => setFilters(prev => ({ ...prev, sortBy: option.value as any }))}
              >
                <Text style={[
                  styles.optionText,
                  filters.sortBy === option.value && styles.optionTextSelected
                ]}>
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Apply Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.applyButton}
          onPress={applyFilters}
        >
          <Filter size={20} color="#FFFFFF" />
          <Text style={styles.applyButtonText}>
            Apply Filters {getActiveFiltersCount() > 0 && `(${getActiveFiltersCount()})`}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    fontFamily: 'Inter-SemiBold',
  },
  clearButton: {
    padding: 8,
    marginRight: -8,
  },
  clearButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#EF4444',
    fontFamily: 'Inter-Medium',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
    fontFamily: 'Inter-SemiBold',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#111827',
    fontFamily: 'Inter-Regular',
  },
  sportsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  sportChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    marginBottom: 8,
  },
  sportChipSelected: {
    backgroundColor: '#EF4444',
    borderColor: '#EF4444',
  },
  sportIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  sportName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    fontFamily: 'Inter-Medium',
    marginRight: 6,
  },
  sportNameSelected: {
    color: '#FFFFFF',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  locationInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#111827',
    fontFamily: 'Inter-Regular',
  },
  priceRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  priceInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  priceInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: '#111827',
    fontFamily: 'Inter-Regular',
  },
  priceSeparator: {
    fontSize: 14,
    color: '#6B7280',
    fontFamily: 'Inter-Regular',
  },
  priceRangeText: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 8,
    fontFamily: 'Inter-Regular',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  ratingButton: {
    padding: 4,
  },
  ratingButtonSelected: {
    backgroundColor: '#FEF3C7',
    borderRadius: 6,
  },
  ratingText: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 8,
    fontFamily: 'Inter-Regular',
  },
  optionsContainer: {
    gap: 8,
  },
  optionButton: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  optionButtonSelected: {
    backgroundColor: '#FEF2F2',
    borderColor: '#EF4444',
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    fontFamily: 'Inter-Medium',
  },
  optionTextSelected: {
    color: '#EF4444',
  },
  amenitiesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  amenityChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    marginBottom: 8,
  },
  amenityChipSelected: {
    backgroundColor: '#EF4444',
    borderColor: '#EF4444',
  },
  amenityText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#374151',
    fontFamily: 'Inter-Medium',
    marginRight: 4,
  },
  amenityTextSelected: {
    color: '#FFFFFF',
  },
  bottomSpacing: {
    height: 100,
  },
  footer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
  applyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#EF4444',
    borderRadius: 12,
    paddingVertical: 16,
    gap: 8,
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    fontFamily: 'Inter-SemiBold',
  },
});
